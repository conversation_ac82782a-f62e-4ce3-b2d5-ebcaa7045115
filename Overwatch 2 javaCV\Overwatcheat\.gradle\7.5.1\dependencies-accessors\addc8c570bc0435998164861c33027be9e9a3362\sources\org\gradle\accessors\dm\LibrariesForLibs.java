package org.gradle.accessors.dm;

import org.gradle.api.NonNullApi;
import org.gradle.api.artifacts.MinimalExternalModuleDependency;
import org.gradle.plugin.use.PluginDependency;
import org.gradle.api.artifacts.ExternalModuleDependencyBundle;
import org.gradle.api.artifacts.MutableVersionConstraint;
import org.gradle.api.provider.Provider;
import org.gradle.api.provider.ProviderFactory;
import org.gradle.api.internal.catalog.AbstractExternalDependencyFactory;
import org.gradle.api.internal.catalog.DefaultVersionCatalog;
import java.util.Map;
import javax.inject.Inject;

/**
 * A catalog of dependencies accessible via the `libs` extension.
*/
@NonNullApi
public class LibrariesForLibs extends AbstractExternalDependencyFactory {

    private final AbstractExternalDependencyFactory owner = this;
    private final GdxLibraryAccessors laccForGdxLibraryAccessors = new GdxLibraryAccessors(owner);
    private final JavacvLibraryAccessors laccForJavacvLibraryAccessors = new JavacvLibraryAccessors(owner);
    private final JnaLibraryAccessors laccForJnaLibraryAccessors = new JnaLibraryAccessors(owner);
    private final VisLibraryAccessors laccForVisLibraryAccessors = new VisLibraryAccessors(owner);
    private final VersionAccessors vaccForVersionAccessors = new VersionAccessors(providers, config);
    private final BundleAccessors baccForBundleAccessors = new BundleAccessors(providers, config);
    private final PluginAccessors paccForPluginAccessors = new PluginAccessors(providers, config);

    @Inject
    public LibrariesForLibs(DefaultVersionCatalog config, ProviderFactory providers) {
        super(config, providers);
    }

        /**
         * Creates a dependency provider for fastutil (it.unimi.dsi:fastutil)
         * This dependency was declared in plugin 'overwatcheat-settings'
         */
        public Provider<MinimalExternalModuleDependency> getFastutil() { return create("fastutil"); }

    /**
     * Returns the group of libraries at gdx
     */
    public GdxLibraryAccessors getGdx() { return laccForGdxLibraryAccessors; }

    /**
     * Returns the group of libraries at javacv
     */
    public JavacvLibraryAccessors getJavacv() { return laccForJavacvLibraryAccessors; }

    /**
     * Returns the group of libraries at jna
     */
    public JnaLibraryAccessors getJna() { return laccForJnaLibraryAccessors; }

    /**
     * Returns the group of libraries at vis
     */
    public VisLibraryAccessors getVis() { return laccForVisLibraryAccessors; }

    /**
     * Returns the group of versions at versions
     */
    public VersionAccessors getVersions() { return vaccForVersionAccessors; }

    /**
     * Returns the group of bundles at bundles
     */
    public BundleAccessors getBundles() { return baccForBundleAccessors; }

    /**
     * Returns the group of plugins at plugins
     */
    public PluginAccessors getPlugins() { return paccForPluginAccessors; }

    public static class GdxLibraryAccessors extends SubDependencyFactory implements DependencyNotationSupplier {
        private final GdxBackendLibraryAccessors laccForGdxBackendLibraryAccessors = new GdxBackendLibraryAccessors(owner);
        private final GdxBox2dLibraryAccessors laccForGdxBox2dLibraryAccessors = new GdxBox2dLibraryAccessors(owner);
        private final GdxFreetypeLibraryAccessors laccForGdxFreetypeLibraryAccessors = new GdxFreetypeLibraryAccessors(owner);

        public GdxLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for gdx (com.badlogicgames.gdx:gdx)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> asProvider() { return create("gdx"); }

            /**
             * Creates a dependency provider for platform (com.badlogicgames.gdx:gdx-platform)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getPlatform() { return create("gdx.platform"); }

        /**
         * Returns the group of libraries at gdx.backend
         */
        public GdxBackendLibraryAccessors getBackend() { return laccForGdxBackendLibraryAccessors; }

        /**
         * Returns the group of libraries at gdx.box2d
         */
        public GdxBox2dLibraryAccessors getBox2d() { return laccForGdxBox2dLibraryAccessors; }

        /**
         * Returns the group of libraries at gdx.freetype
         */
        public GdxFreetypeLibraryAccessors getFreetype() { return laccForGdxFreetypeLibraryAccessors; }

    }

    public static class GdxBackendLibraryAccessors extends SubDependencyFactory {

        public GdxBackendLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for lwjgl3 (com.badlogicgames.gdx:gdx-backend-lwjgl3)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getLwjgl3() { return create("gdx.backend.lwjgl3"); }

    }

    public static class GdxBox2dLibraryAccessors extends SubDependencyFactory implements DependencyNotationSupplier {

        public GdxBox2dLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for box2d (com.badlogicgames.gdx:gdx-box2d)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> asProvider() { return create("gdx.box2d"); }

            /**
             * Creates a dependency provider for platform (com.badlogicgames.gdx:gdx-box2d-platform)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getPlatform() { return create("gdx.box2d.platform"); }

    }

    public static class GdxFreetypeLibraryAccessors extends SubDependencyFactory implements DependencyNotationSupplier {

        public GdxFreetypeLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for freetype (com.badlogicgames.gdx:gdx-freetype)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> asProvider() { return create("gdx.freetype"); }

            /**
             * Creates a dependency provider for platform (com.badlogicgames.gdx:gdx-freetype-platform)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getPlatform() { return create("gdx.freetype.platform"); }

    }

    public static class JavacvLibraryAccessors extends SubDependencyFactory {

        public JavacvLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for platform (org.bytedeco:javacv-platform)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getPlatform() { return create("javacv.platform"); }

    }

    public static class JnaLibraryAccessors extends SubDependencyFactory implements DependencyNotationSupplier {

        public JnaLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for jna (net.java.dev.jna:jna)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> asProvider() { return create("jna"); }

            /**
             * Creates a dependency provider for platform (net.java.dev.jna:jna-platform)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getPlatform() { return create("jna.platform"); }

    }

    public static class VisLibraryAccessors extends SubDependencyFactory {

        public VisLibraryAccessors(AbstractExternalDependencyFactory owner) { super(owner); }

            /**
             * Creates a dependency provider for ui (com.kotcrab.vis:vis-ui)
             * This dependency was declared in plugin 'overwatcheat-settings'
             */
            public Provider<MinimalExternalModuleDependency> getUi() { return create("vis.ui"); }

    }

    public static class VersionAccessors extends VersionFactory  {

        public VersionAccessors(ProviderFactory providers, DefaultVersionCatalog config) { super(providers, config); }

            /**
             * Returns the version associated to this alias: gdx (1.11.0)
             * If the version is a rich version and that its not expressible as a
             * single version string, then an empty string is returned.
             * This version was declared in plugin 'overwatcheat-settings'
             */
            public Provider<String> getGdx() { return getVersion("gdx"); }

            /**
             * Returns the version associated to this alias: jna (5.12.1)
             * If the version is a rich version and that its not expressible as a
             * single version string, then an empty string is returned.
             * This version was declared in plugin 'overwatcheat-settings'
             */
            public Provider<String> getJna() { return getVersion("jna"); }

            /**
             * Returns the version associated to this alias: kotlin (1.7.20)
             * If the version is a rich version and that its not expressible as a
             * single version string, then an empty string is returned.
             * This version was declared in plugin 'overwatcheat-settings'
             */
            public Provider<String> getKotlin() { return getVersion("kotlin"); }

    }

    public static class BundleAccessors extends BundleFactory {

        public BundleAccessors(ProviderFactory providers, DefaultVersionCatalog config) { super(providers, config); }

    }

    public static class PluginAccessors extends PluginFactory {

        public PluginAccessors(ProviderFactory providers, DefaultVersionCatalog config) { super(providers, config); }

    }

}
