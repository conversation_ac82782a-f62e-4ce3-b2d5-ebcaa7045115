<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="OTHER_INDENT_OPTIONS">
      <value>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
      </value>
    </option>
    <HTMLCodeStyleSettings>
      <option name="HTML_UNIFORM_INDENT" value="true" />
    </HTMLCodeStyleSettings>
    <JetCodeStyleSettings>
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </JetCodeStyleSettings>
    <codeStyleSettings language="Gherkin">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="Groovy">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="HTML">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JAVA">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JSON">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="MySQL">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="PostgreSQL">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="SQLite">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="XML">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="kotlin">
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </codeStyleSettings>
    <codeStyleSettings language="yaml">
      <indentOptions>
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>