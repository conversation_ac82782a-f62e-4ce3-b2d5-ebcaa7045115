# Overwatcheat

_Free, open-source undetected color cheat for Overwatch!_

[![Build Status](https://travis-ci.org/Jire/Overwatcheat.svg?branch=master)](https://travis-ci.org/Jire/Overwatcheat)
[![License](https://img.shields.io/github/license/Jire/Overwatcheat.svg)](https://github.com/Jire/Overwatcheat/blob/master/LICENSE.txt)

**Available Features**

* Incredibly efficient frame grabber
* Optimized zero-garbage frame pixel scanner
* Extremely low CPU and memory usage, with minimal effect on FPS
* Magenta-pixel HP bar aimbot
* Uses Interception driver to avoid mouse event detection
* Capable of finding an OBS projector window, in case of color ban (error 5)

**Beginner's Guide**

Before doing anything, make sure you have Java Development Kit (JDK) 8 or later installed.

The first step is to compile the source code into a usable cheat program.

* Visit our [GitHub repository](https://github.com/Jire/Overwatcheat)
* Click on the [**Releases**](https://github.com/Jire/Overwatcheat/releases) tab
* Download the latest release ZIP archive
* Extract the contents from the archive to any folder (to your desktop, for example)
* Make sure you have Java Development Kit 8 or later installed
* Double click on the "_build_" (_build.bat_) script and wait for it to complete

Once those steps are complete, the usable cheat program can be found within the _build_
directory, and will in a directory called **Overwatcheat 3.5.0**.

From within the **Overwatcheat 3.5.0** directory, you can start the cheat by running the _"Start Overwatcheat 3.5.0"_
script.

**Requirements:**

* Use fullscreen windowed mode if not using OBS projector
* It is vital to configure sensitivity to be the same as your in-game sens
* Increasing sensitivity to beyond your sens will give you slower aim
* Increasing FPS will increase accuracy

---

**Installation & Usage Guide**

[![Installation & Usage Guide](https://i.imgur.com/p6qyqkT.png)](https://www.youtube.com/watch?v=oJGQbK6iYLM "Overwatcheat")

---

**Copyright** (AGPL-3.0)

```
Free, open-source undetected color cheat for Overwatch!
Copyright (C) 2017  Thomas G. Nappo

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU Affero General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Affero General Public License for more details.

You should have received a copy of the GNU Affero General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.
```
