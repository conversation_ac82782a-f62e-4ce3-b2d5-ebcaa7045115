# مساعد التصويب - Overwatch 2
## الواجهة العربية الاحترافية

### نظرة عامة
هذا برنامج مساعد للتصويب في لعبة Overwatch 2 مع واجهة عربية احترافية تتيح لك:
- إعدادات منفصلة لكل شخصية من شخصيات اللعبة
- تحكم كامل في جميع معاملات التصويب
- واجهة سهلة الاستخدام باللغة العربية
- معاينة مباشرة للإعدادات
- حفظ وتحميل ملفات التعريف

### الميزات الرئيسية

#### 🎯 إعدادات التصويب المتقدمة
- **وضعين للتصويب**: تتبع مستمر أو فليك سريع
- **حساسية قابلة للتخصيص**: من 0.1 إلى 100
- **تحكم في السرعة**: مدة التصويب ومضاعفات التوقيت
- **تنعيم الحركة**: لحركة طبيعية أكثر
- **التنبؤ بالحركة**: لاستهداف الأعداء المتحركين

#### 👥 إعدادات منفصلة للشخصيات
- **جميع شخصيات Overwatch 2**: 35+ شخصية مدعومة
- **إعدادات مخصصة**: لكل شخصية إعداداتها الخاصة
- **تصنيف حسب الدور**: دبابة، ضرر، دعم
- **إعدادات افتراضية ذكية**: محسنة لكل شخصية

#### 🎨 واجهة عربية احترافية
- **تصميم عصري**: واجهة جميلة وسهلة الاستخدام
- **دعم كامل للعربية**: جميع النصوص والقوائم
- **تبويبات منظمة**: للوصول السريع للإعدادات
- **معاينة مباشرة**: لرؤية تأثير التغييرات فوراً

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11
- **Java**: الإصدار 17 أو أحدث
- **الذاكرة**: 2 جيجابايت RAM على الأقل
- **المعالج**: Intel i5 أو AMD Ryzen 5 أو أفضل
- **اللعبة**: Overwatch 2

### طريقة التثبيت

#### 1. تحميل المتطلبات
```bash
# تثبيت Java 17
# قم بتحميله من: https://adoptium.net/

# تحميل المشروع
git clone [repository-url]
cd overwatcheat
```

#### 2. بناء المشروع
```bash
# على Windows
gradlew.bat build

# على Linux/Mac
./gradlew build
```

#### 3. تشغيل الواجهة
```bash
# الطريقة الأولى: استخدام ملف batch
start_gui.bat

# الطريقة الثانية: تشغيل مباشر
java -jar build/libs/Overwatcheat-*.jar --gui

# الطريقة الثالثة: النسخة الأصلية بدون واجهة
java -jar build/libs/Overwatcheat-*.jar
```

### دليل الاستخدام

#### البدء السريع
1. **شغل الواجهة** باستخدام `start_gui.bat`
2. **اختر الشخصية** من تبويب "الشخصيات"
3. **اضبط الإعدادات** في تبويب "إعدادات التصويب"
4. **احفظ الإعدادات** من قائمة "ملف"
5. **ابدأ اللعب** واستمتع بالتصويب المحسن!

#### التبويبات الرئيسية

##### 🎮 تبويب الشخصيات
- **اختيار الشخصية**: شبكة تفاعلية لجميع الشخصيات
- **فلترة حسب الدور**: دبابة، ضرر، أو دعم
- **البحث**: ابحث عن شخصية بالاسم
- **معلومات الشخصية**: وصف وإعدادات افتراضية

##### ⚙️ تبويب الإعدادات العامة
- **معدل الإطارات (FPS)**: 30-240 إطار/ثانية
- **صندوق الالتقاط**: حجم منطقة المسح
- **النافذة المستهدفة**: عادة "Overwatch"
- **تفعيل الأوفرلاي**: لعرض معلومات إضافية

##### 🎯 تبويب إعدادات التصويب
- **الحساسية العامة**: التحكم في سرعة الحركة
- **وضع التصويب**: تتبع أو فليك
- **مدة التصويب**: سرعة الاستجابة
- **التنعيم**: لحركة طبيعية
- **الإعدادات المسبقة**: للمبتدئين والمحترفين

##### 🎨 تبويب الألوان المستهدفة
- **اختيار الألوان**: الألوان التي يستهدفها البرنامج
- **تسامح اللون**: مدى قبول الألوان المشابهة
- **معاينة الألوان**: لرؤية الألوان المختارة

##### 👁️ تبويب المعاينة المباشرة
- **عرض منطقة الالتقاط**: ما يراه البرنامج
- **إحصائيات الأداء**: FPS ودقة الاستهداف
- **اختبار التصويب**: لتجربة الإعدادات

### الإعدادات المتقدمة

#### إعدادات الشخصيات
كل شخصية لها إعداداتها المخصصة:

**شخصيات التتبع** (مثل Soldier: 76, Tracer):
- حساسية عالية (15-25)
- مدة تصويب متوسطة (3-4ms)
- تنعيم مفعل

**شخصيات الفليك** (مثل Widowmaker, Hanzo):
- حساسية منخفضة (5-10)
- مدة تصويب طويلة (5-8ms)
- دقة عالية

**شخصيات الدبابة** (مثل Reinhardt, Winston):
- إعدادات متوازنة
- تركيز على الاستقرار

#### ملفات التعريف
- **حفظ متعدد**: احفظ إعدادات مختلفة
- **تصدير/استيراد**: شارك الإعدادات مع الأصدقاء
- **نسخ احتياطي**: حماية إعداداتك

### استكشاف الأخطاء

#### مشاكل شائعة وحلولها

**البرنامج لا يبدأ:**
- تأكد من تثبيت Java 17+
- تحقق من بناء المشروع بنجاح
- شغل كمدير (Run as Administrator)

**لا يتم اكتشاف اللعبة:**
- تأكد من أن اللعبة تعمل
- غير "عنوان النافذة المستهدفة" إلى "Overwatch"
- جرب "desktop" إذا كنت تستخدم OBS

**التصويب لا يعمل:**
- تحقق من الألوان المستهدفة
- اضبط تسامح اللون
- تأكد من أن مفتاح التصويب صحيح

**أداء ضعيف:**
- قلل معدل الإطارات
- صغر صندوق الالتقاط
- فعل تحسين المعالج

### الأمان والقانونية

⚠️ **تحذير مهم**: 
- هذا البرنامج للأغراض التعليمية فقط
- استخدمه على مسؤوليتك الخاصة
- قد يؤدي إلى حظر الحساب
- لا نتحمل مسؤولية أي عواقب

### المساهمة في التطوير

نرحب بمساهماتكم في تطوير البرنامج:

1. **Fork** المشروع
2. **إنشاء فرع** للميزة الجديدة
3. **Commit** التغييرات
4. **Push** إلى الفرع
5. **إنشاء Pull Request**

### الدعم والمساعدة

- **GitHub Issues**: لتقارير الأخطاء
- **Discussions**: للأسئلة والاقتراحات
- **Wiki**: للوثائق المفصلة

### الترخيص

هذا المشروع مرخص تحت رخصة GNU Affero General Public License v3.0

### شكر خاص

- **المطور الأصلي**: Thomas G. Nappo
- **مجتمع Overwatch**: للدعم والاختبار
- **المساهمون**: جميع من ساهم في التطوير

---

**استمتع بتجربة تصويب محسنة مع الواجهة العربية الاحترافية! 🎯**
