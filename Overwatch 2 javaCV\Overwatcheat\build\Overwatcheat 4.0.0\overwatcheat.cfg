# The virtual key code key which activates the aim bot when held.
aim_key=1

# 0 for tracking (e.g. <PERSON>, <PERSON><PERSON>), 1 for flicking (e.g. <PERSON><PERSON><PERSON><PERSON>, <PERSON>)
aim_mode=0

# Your in-game sensitivity.
sensitivity=1.5

# FPS to scan game frames.
fps=25

# Aim duration per snap.
aim_duration_millis=0.78

# Smoothing settings
aim_duration_multiplier_base=5
aim_duration_multiplier_max=0.6

# Max pixels to move per frame
aim_max_move_pixels=3

# Human-like aim randomness
aim_jitter_percent=64

# Target box size
aim_min_target_width=1
aim_min_target_height=1

box_width=64
box_height=64
max_snap_divisor=0.71

# Target color settings
target_colors=d521cd,d722cf,d623ce,d722ce,d621cd,ce19ca,d11ccb,d21dca,c818cf,d722cd,d722ce,cd19c9,c617d3,cb17c5,da25d3,ce24cc,d328cc,db32ef,bd15c4,dc5bea,da59eb,d959e9,f444fb,cf1ac9,d422d4,d923cd,e53af2,d321d3,e539f3,e035ed,d822cc,e83df5,d11fd1,d622d0,d21dcc,d429e2,e537ef,d923cd,e136ee,d321d3,e63bf3,d722cf,e036ee,d72ce6,d428e1,d321d3,d21dcc,df34ed,d822cc,e434e6,d43ddf,de30e4,be0dbe,d823d3,c814c4,c20ab7,de1ec1,ca16c6,c30ebe,bb0fbf,c510bf,c10cbc,d21cb6,ca14c5,b80cd1,ae0ea8,bf0ec3,d415c1,bc22b7,d317c4,b1179d,bc0fb4,cc47c7,b834b5,dc2cd9,d727d5,de30da,c834c6

target_color_tolerance=19

# Window title to detect
window_title_search=desktop

# Device ID
device_id=11

# Offset adjustments
aim_offset_x=1.00
aim_offset_y=0.98

# Flick shooting
flick_shoot_pixels=5
flick_pause_duration=50

# Overlay
enable_overlay=false
