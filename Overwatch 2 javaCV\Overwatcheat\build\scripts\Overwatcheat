#!/bin/sh

#
# Copyright © 2015-2021 the original authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##############################################################################
#
#   Overwatcheat start up script for POSIX generated by Gradle.
#
#   Important for running:
#
#   (1) You need a POSIX-compliant shell to run this script. If your /bin/sh is
#       noncompliant, but you have some other compliant shell such as ksh or
#       bash, then to run this script, type that shell name before the whole
#       command line, like:
#
#           ksh Overwatcheat
#
#       Busybox and similar reduced shells will NOT work, because this script
#       requires all of these POSIX shell features:
#         * functions;
#         * expansions «$var», «${var}», «${var:-default}», «${var+SET}»,
#           «${var#prefix}», «${var%suffix}», and «$( cmd )»;
#         * compound commands having a testable exit status, especially «case»;
#         * various built-in commands including «command», «set», and «ulimit».
#
#   Important for patching:
#
#   (2) This script targets any POSIX shell, so it avoids extensions provided
#       by Bash, Ksh, etc; in particular arrays are avoided.
#
#       The "traditional" practice of packing multiple parameters into a
#       space-separated string is a well documented source of bugs and security
#       problems, so this is (mostly) avoided, by progressively accumulating
#       options in "$@", and eventually passing that to Java.
#
#       Where the inherited environment variables (DEFAULT_JVM_OPTS, JAVA_OPTS,
#       and OVERWATCHEAT_OPTS) rely on word-splitting, this is performed explicitly;
#       see the in-line comments for details.
#
#       There are tweaks for specific operating systems such as AIX, CygWin,
#       Darwin, MinGW, and NonStop.
#
#   (3) This script is generated from the Groovy template
#       https://github.com/gradle/gradle/blob/master/subprojects/plugins/src/main/resources/org/gradle/api/internal/plugins/unixStartScript.txt
#       within the Gradle project.
#
#       You can find Gradle at https://github.com/gradle/gradle/.
#
##############################################################################

# Attempt to set APP_HOME

# Resolve links: $0 may be a link
app_path=$0

# Need this for daisy-chained symlinks.
while
    APP_HOME=${app_path%"${app_path##*/}"}  # leaves a trailing /; empty if no leading path
    [ -h "$app_path" ]
do
    ls=$( ls -ld "$app_path" )
    link=${ls#*' -> '}
    case $link in             #(
      /*)   app_path=$link ;; #(
      *)    app_path=$APP_HOME$link ;;
    esac
done

APP_HOME=$( cd "${APP_HOME:-./}.." && pwd -P ) || exit

APP_NAME="Overwatcheat"
APP_BASE_NAME=${0##*/}

# Add default JVM options here. You can also use JAVA_OPTS and OVERWATCHEAT_OPTS to pass JVM options to this script.
DEFAULT_JVM_OPTS=""

# Use the maximum available, or set MAX_FD != -1 to use that value.
MAX_FD=maximum

warn () {
    echo "$*"
} >&2

die () {
    echo
    echo "$*"
    echo
    exit 1
} >&2

# OS specific support (must be 'true' or 'false').
cygwin=false
msys=false
darwin=false
nonstop=false
case "$( uname )" in                #(
  CYGWIN* )         cygwin=true  ;; #(
  Darwin* )         darwin=true  ;; #(
  MSYS* | MINGW* )  msys=true    ;; #(
  NONSTOP* )        nonstop=true ;;
esac

CLASSPATH=$APP_HOME/lib/Overwatcheat-4.0.0.jar:$APP_HOME/lib/kotlin-stdlib-jdk8-1.7.20.jar:$APP_HOME/lib/kotlin-stdlib-jdk7-1.7.20.jar:$APP_HOME/lib/kotlin-stdlib-1.7.20.jar:$APP_HOME/lib/kotlin-stdlib-common-1.7.20.jar:$APP_HOME/lib/fastutil-8.5.9.jar:$APP_HOME/lib/javacv-platform-1.5.7.jar:$APP_HOME/lib/vis-ui-1.5.0.jar:$APP_HOME/lib/jna-platform-5.12.1.jar:$APP_HOME/lib/jna-5.12.1.jar:$APP_HOME/lib/gdx-box2d-1.11.0.jar:$APP_HOME/lib/gdx-freetype-1.11.0.jar:$APP_HOME/lib/gdx-backend-lwjgl3-1.11.0.jar:$APP_HOME/lib/gdx-1.11.0.jar:$APP_HOME/lib/annotations-13.0.jar:$APP_HOME/lib/javacv-1.5.7.jar:$APP_HOME/lib/flandmark-platform-1.07-1.5.7.jar:$APP_HOME/lib/opencv-platform-4.5.5-1.5.7.jar:$APP_HOME/lib/openblas-platform-0.3.19-1.5.7.jar:$APP_HOME/lib/ffmpeg-platform-5.0-1.5.7.jar:$APP_HOME/lib/flycapture-platform-2.13.3.31-1.5.7.jar:$APP_HOME/lib/libdc1394-platform-2.2.6-1.5.7.jar:$APP_HOME/lib/libfreenect-platform-0.5.7-1.5.7.jar:$APP_HOME/lib/libfreenect2-platform-0.2.0-1.5.7.jar:$APP_HOME/lib/librealsense-platform-1.12.4-1.5.7.jar:$APP_HOME/lib/librealsense2-platform-2.50.0-1.5.7.jar:$APP_HOME/lib/videoinput-platform-0.200-1.5.7.jar:$APP_HOME/lib/artoolkitplus-platform-2.3.1-1.5.7.jar:$APP_HOME/lib/tesseract-platform-5.0.1-1.5.7.jar:$APP_HOME/lib/leptonica-platform-1.82.0-1.5.7.jar:$APP_HOME/lib/gdx-jnigen-loader-2.3.1.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-linux.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-linux-arm32.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-linux-arm64.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-macos.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-macos-arm64.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-windows.jar:$APP_HOME/lib/lwjgl-glfw-3.3.1-natives-windows-x86.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-linux.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-linux-arm32.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-linux-arm64.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-macos.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-macos-arm64.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-windows.jar:$APP_HOME/lib/lwjgl-jemalloc-3.3.1-natives-windows-x86.jar:$APP_HOME/lib/lwjgl-openal-3.3.1.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-linux.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-linux-arm32.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-linux-arm64.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-macos.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-macos-arm64.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-windows.jar:$APP_HOME/lib/lwjgl-openal-3.3.1-natives-windows-x86.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-linux.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-linux-arm32.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-linux-arm64.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-macos.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-macos-arm64.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-windows.jar:$APP_HOME/lib/lwjgl-opengl-3.3.1-natives-windows-x86.jar:$APP_HOME/lib/lwjgl-stb-3.3.1.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-linux.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-linux-arm32.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-linux-arm64.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-macos.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-macos-arm64.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-windows.jar:$APP_HOME/lib/lwjgl-stb-3.3.1-natives-windows-x86.jar:$APP_HOME/lib/lwjgl-3.3.1.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-linux.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-linux-arm32.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-linux-arm64.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-macos.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-macos-arm64.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-windows.jar:$APP_HOME/lib/lwjgl-3.3.1-natives-windows-x86.jar:$APP_HOME/lib/jlayer-1.0.1-gdx.jar:$APP_HOME/lib/jorbis-0.0.17.jar:$APP_HOME/lib/flandmark-1.07-1.5.7.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-android-arm.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-android-arm64.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-android-x86.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-android-x86_64.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-linux-x86.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-linux-x86_64.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-linux-armhf.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-linux-arm64.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-windows-x86.jar:$APP_HOME/lib/flandmark-1.07-1.5.7-windows-x86_64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-android-arm.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-android-arm64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-android-x86.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-android-x86_64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-ios-arm64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-ios-x86_64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-linux-x86.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-linux-x86_64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-linux-armhf.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-linux-arm64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-macosx-arm64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-windows-x86.jar:$APP_HOME/lib/opencv-4.5.5-1.5.7-windows-x86_64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-android-arm.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-android-arm64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-android-x86.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-android-x86_64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-ios-arm64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-ios-x86_64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-linux-x86.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-linux-x86_64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-linux-armhf.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-linux-arm64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-macosx-arm64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-windows-x86.jar:$APP_HOME/lib/openblas-0.3.19-1.5.7-windows-x86_64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-android-arm.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-android-arm64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-android-x86.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-android-x86_64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-linux-x86.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-linux-x86_64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-linux-armhf.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-linux-arm64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-macosx-arm64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-windows-x86.jar:$APP_HOME/lib/ffmpeg-5.0-1.5.7-windows-x86_64.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7-linux-x86.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7-linux-x86_64.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7-linux-armhf.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7-linux-arm64.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7-windows-x86.jar:$APP_HOME/lib/flycapture-2.13.3.31-1.5.7-windows-x86_64.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-linux-x86.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-linux-x86_64.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-linux-armhf.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-linux-arm64.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-windows-x86.jar:$APP_HOME/lib/libdc1394-2.2.6-1.5.7-windows-x86_64.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-linux-x86.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-linux-x86_64.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-linux-armhf.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-linux-arm64.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-windows-x86.jar:$APP_HOME/lib/libfreenect-0.5.7-1.5.7-windows-x86_64.jar:$APP_HOME/lib/libfreenect2-0.2.0-1.5.7.jar:$APP_HOME/lib/libfreenect2-0.2.0-1.5.7-linux-x86.jar:$APP_HOME/lib/libfreenect2-0.2.0-1.5.7-linux-x86_64.jar:$APP_HOME/lib/libfreenect2-0.2.0-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/libfreenect2-0.2.0-1.5.7-windows-x86_64.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-linux-armhf.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-linux-arm64.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-linux-x86.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-linux-x86_64.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-windows-x86.jar:$APP_HOME/lib/librealsense-1.12.4-1.5.7-windows-x86_64.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-linux-armhf.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-linux-arm64.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-linux-x86.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-linux-x86_64.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-windows-x86.jar:$APP_HOME/lib/librealsense2-2.50.0-1.5.7-windows-x86_64.jar:$APP_HOME/lib/videoinput-0.200-1.5.7.jar:$APP_HOME/lib/videoinput-0.200-1.5.7-windows-x86.jar:$APP_HOME/lib/videoinput-0.200-1.5.7-windows-x86_64.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-android-arm.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-android-arm64.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-android-x86.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-android-x86_64.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-linux-x86.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-linux-x86_64.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-linux-armhf.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-linux-arm64.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-windows-x86.jar:$APP_HOME/lib/artoolkitplus-2.3.1-1.5.7-windows-x86_64.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-android-arm.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-android-arm64.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-android-x86.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-android-x86_64.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-linux-x86.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-linux-x86_64.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-linux-armhf.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-linux-arm64.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-windows-x86.jar:$APP_HOME/lib/tesseract-5.0.1-1.5.7-windows-x86_64.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-android-arm.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-android-arm64.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-android-x86.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-android-x86_64.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-linux-x86.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-linux-x86_64.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-linux-armhf.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-linux-arm64.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-windows-x86.jar:$APP_HOME/lib/leptonica-1.82.0-1.5.7-windows-x86_64.jar:$APP_HOME/lib/javacpp-platform-1.5.7.jar:$APP_HOME/lib/javacpp-1.5.7.jar:$APP_HOME/lib/javacpp-1.5.7-android-arm.jar:$APP_HOME/lib/javacpp-1.5.7-android-arm64.jar:$APP_HOME/lib/javacpp-1.5.7-android-x86.jar:$APP_HOME/lib/javacpp-1.5.7-android-x86_64.jar:$APP_HOME/lib/javacpp-1.5.7-ios-arm64.jar:$APP_HOME/lib/javacpp-1.5.7-ios-x86_64.jar:$APP_HOME/lib/javacpp-1.5.7-linux-armhf.jar:$APP_HOME/lib/javacpp-1.5.7-linux-arm64.jar:$APP_HOME/lib/javacpp-1.5.7-linux-ppc64le.jar:$APP_HOME/lib/javacpp-1.5.7-linux-x86.jar:$APP_HOME/lib/javacpp-1.5.7-linux-x86_64.jar:$APP_HOME/lib/javacpp-1.5.7-macosx-arm64.jar:$APP_HOME/lib/javacpp-1.5.7-macosx-x86_64.jar:$APP_HOME/lib/javacpp-1.5.7-windows-x86.jar:$APP_HOME/lib/javacpp-1.5.7-windows-x86_64.jar


# Determine the Java command to use to start the JVM.
if [ -n "$JAVA_HOME" ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
        # IBM's JDK on AIX uses strange locations for the executables
        JAVACMD=$JAVA_HOME/jre/sh/java
    else
        JAVACMD=$JAVA_HOME/bin/java
    fi
    if [ ! -x "$JAVACMD" ] ; then
        die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME

Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
    fi
else
    JAVACMD=java
    which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.

Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
fi

# Increase the maximum file descriptors if we can.
if ! "$cygwin" && ! "$darwin" && ! "$nonstop" ; then
    case $MAX_FD in #(
      max*)
        MAX_FD=$( ulimit -H -n ) ||
            warn "Could not query maximum file descriptor limit"
    esac
    case $MAX_FD in  #(
      '' | soft) :;; #(
      *)
        ulimit -n "$MAX_FD" ||
            warn "Could not set maximum file descriptor limit to $MAX_FD"
    esac
fi

# Collect all arguments for the java command, stacking in reverse order:
#   * args from the command line
#   * the main class name
#   * -classpath
#   * -D...appname settings
#   * --module-path (only if needed)
#   * DEFAULT_JVM_OPTS, JAVA_OPTS, and OVERWATCHEAT_OPTS environment variables.

# For Cygwin or MSYS, switch paths to Windows format before running java
if "$cygwin" || "$msys" ; then
    APP_HOME=$( cygpath --path --mixed "$APP_HOME" )
    CLASSPATH=$( cygpath --path --mixed "$CLASSPATH" )

    JAVACMD=$( cygpath --unix "$JAVACMD" )

    # Now convert the arguments - kludge to limit ourselves to /bin/sh
    for arg do
        if
            case $arg in                                #(
              -*)   false ;;                            # don't mess with options #(
              /?*)  t=${arg#/} t=/${t%%/*}              # looks like a POSIX filepath
                    [ -e "$t" ] ;;                      #(
              *)    false ;;
            esac
        then
            arg=$( cygpath --path --ignore --mixed "$arg" )
        fi
        # Roll the args list around exactly as many times as the number of
        # args, so each arg winds up back in the position where it started, but
        # possibly modified.
        #
        # NB: a `for` loop captures its iteration list before it begins, so
        # changing the positional parameters here affects neither the number of
        # iterations, nor the values presented in `arg`.
        shift                   # remove old arg
        set -- "$@" "$arg"      # push replacement arg
    done
fi

# Collect all arguments for the java command;
#   * $DEFAULT_JVM_OPTS, $JAVA_OPTS, and $OVERWATCHEAT_OPTS can contain fragments of
#     shell script including quotes and variable substitutions, so put them in
#     double quotes to make sure that they get re-expanded; and
#   * put everything else in single quotes, so that it's not re-expanded.

set -- \
        -classpath "$CLASSPATH" \
        org.jire.overwatcheat.Main \
        "$@"

# Stop when "xargs" is not available.
if ! command -v xargs >/dev/null 2>&1
then
    die "xargs is not available"
fi

# Use "xargs" to parse quoted args.
#
# With -n1 it outputs one arg per line, with the quotes and backslashes removed.
#
# In Bash we could simply go:
#
#   readarray ARGS < <( xargs -n1 <<<"$var" ) &&
#   set -- "${ARGS[@]}" "$@"
#
# but POSIX shell has neither arrays nor command substitution, so instead we
# post-process each arg (as a line of input to sed) to backslash-escape any
# character that might be a shell metacharacter, then use eval to reverse
# that process (while maintaining the separation between arguments), and wrap
# the whole thing up as a single "set" statement.
#
# This will of course break if any of these variables contains a newline or
# an unmatched quote.
#

eval "set -- $(
        printf '%s\n' "$DEFAULT_JVM_OPTS $JAVA_OPTS $OVERWATCHEAT_OPTS" |
        xargs -n1 |
        sed ' s~[^-[:alnum:]+,./:=@_]~\\&~g; ' |
        tr '\n' ' '
    )" '"$@"'

exec "$JAVACMD" "$@"
