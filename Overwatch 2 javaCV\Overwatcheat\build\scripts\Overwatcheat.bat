@rem
@rem Copyright 2015 the original author or authors.
@rem
@rem Licensed under the Apache License, Version 2.0 (the "License");
@rem you may not use this file except in compliance with the License.
@rem You may obtain a copy of the License at
@rem
@rem      https://www.apache.org/licenses/LICENSE-2.0
@rem
@rem Unless required by applicable law or agreed to in writing, software
@rem distributed under the License is distributed on an "AS IS" BASIS,
@rem WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
@rem See the License for the specific language governing permissions and
@rem limitations under the License.
@rem

@if "%DEBUG%"=="" @echo off
@rem ##########################################################################
@rem
@rem  Overwatcheat startup script for Windows
@rem
@rem ##########################################################################

@rem Set local scope for the variables with windows NT shell
if "%OS%"=="Windows_NT" setlocal

set DIRNAME=%~dp0
if "%DIRNAME%"=="" set DIRNAME=.
set APP_BASE_NAME=%~n0
set APP_HOME=%DIRNAME%..

@rem Resolve any "." and ".." in APP_HOME to make it shorter.
for %%i in ("%APP_HOME%") do set APP_HOME=%%~fi

@rem Add default JVM options here. You can also use JAVA_OPTS and OVERWATCHEAT_OPTS to pass JVM options to this script.
set DEFAULT_JVM_OPTS=

@rem Find java.exe
if defined JAVA_HOME goto findJavaFromJavaHome

set JAVA_EXE=java.exe
%JAVA_EXE% -version >NUL 2>&1
if %ERRORLEVEL% equ 0 goto execute

echo.
echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
echo.
echo Please set the JAVA_HOME variable in your environment to match the
echo location of your Java installation.

goto fail

:findJavaFromJavaHome
set JAVA_HOME=%JAVA_HOME:"=%
set JAVA_EXE=%JAVA_HOME%/bin/java.exe

if exist "%JAVA_EXE%" goto execute

echo.
echo ERROR: JAVA_HOME is set to an invalid directory: %JAVA_HOME%
echo.
echo Please set the JAVA_HOME variable in your environment to match the
echo location of your Java installation.

goto fail

:execute
@rem Setup the command line

set CLASSPATH=%APP_HOME%\lib\Overwatcheat-4.0.0.jar;%APP_HOME%\lib\kotlin-stdlib-jdk8-1.7.20.jar;%APP_HOME%\lib\kotlin-stdlib-jdk7-1.7.20.jar;%APP_HOME%\lib\kotlin-stdlib-1.7.20.jar;%APP_HOME%\lib\kotlin-stdlib-common-1.7.20.jar;%APP_HOME%\lib\fastutil-8.5.9.jar;%APP_HOME%\lib\javacv-platform-1.5.7.jar;%APP_HOME%\lib\vis-ui-1.5.0.jar;%APP_HOME%\lib\jna-platform-5.12.1.jar;%APP_HOME%\lib\jna-5.12.1.jar;%APP_HOME%\lib\gdx-box2d-1.11.0.jar;%APP_HOME%\lib\gdx-freetype-1.11.0.jar;%APP_HOME%\lib\gdx-backend-lwjgl3-1.11.0.jar;%APP_HOME%\lib\gdx-1.11.0.jar;%APP_HOME%\lib\annotations-13.0.jar;%APP_HOME%\lib\javacv-1.5.7.jar;%APP_HOME%\lib\flandmark-platform-1.07-1.5.7.jar;%APP_HOME%\lib\opencv-platform-4.5.5-1.5.7.jar;%APP_HOME%\lib\openblas-platform-0.3.19-1.5.7.jar;%APP_HOME%\lib\ffmpeg-platform-5.0-1.5.7.jar;%APP_HOME%\lib\flycapture-platform-2.13.3.31-1.5.7.jar;%APP_HOME%\lib\libdc1394-platform-2.2.6-1.5.7.jar;%APP_HOME%\lib\libfreenect-platform-0.5.7-1.5.7.jar;%APP_HOME%\lib\libfreenect2-platform-0.2.0-1.5.7.jar;%APP_HOME%\lib\librealsense-platform-1.12.4-1.5.7.jar;%APP_HOME%\lib\librealsense2-platform-2.50.0-1.5.7.jar;%APP_HOME%\lib\videoinput-platform-0.200-1.5.7.jar;%APP_HOME%\lib\artoolkitplus-platform-2.3.1-1.5.7.jar;%APP_HOME%\lib\tesseract-platform-5.0.1-1.5.7.jar;%APP_HOME%\lib\leptonica-platform-1.82.0-1.5.7.jar;%APP_HOME%\lib\gdx-jnigen-loader-2.3.1.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-linux.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-linux-arm32.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-linux-arm64.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-macos.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-macos-arm64.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-windows.jar;%APP_HOME%\lib\lwjgl-glfw-3.3.1-natives-windows-x86.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-linux.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-linux-arm32.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-linux-arm64.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-macos.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-macos-arm64.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-windows.jar;%APP_HOME%\lib\lwjgl-jemalloc-3.3.1-natives-windows-x86.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-linux.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-linux-arm32.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-linux-arm64.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-macos.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-macos-arm64.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-windows.jar;%APP_HOME%\lib\lwjgl-openal-3.3.1-natives-windows-x86.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-linux.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-linux-arm32.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-linux-arm64.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-macos.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-macos-arm64.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-windows.jar;%APP_HOME%\lib\lwjgl-opengl-3.3.1-natives-windows-x86.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-linux.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-linux-arm32.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-linux-arm64.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-macos.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-macos-arm64.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-windows.jar;%APP_HOME%\lib\lwjgl-stb-3.3.1-natives-windows-x86.jar;%APP_HOME%\lib\lwjgl-3.3.1.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-linux.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-linux-arm32.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-linux-arm64.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-macos.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-macos-arm64.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-windows.jar;%APP_HOME%\lib\lwjgl-3.3.1-natives-windows-x86.jar;%APP_HOME%\lib\jlayer-1.0.1-gdx.jar;%APP_HOME%\lib\jorbis-0.0.17.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-android-arm.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-android-arm64.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-android-x86.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-android-x86_64.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-linux-x86.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-linux-armhf.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-linux-arm64.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-windows-x86.jar;%APP_HOME%\lib\flandmark-1.07-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-android-arm.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-android-arm64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-android-x86.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-android-x86_64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-ios-arm64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-ios-x86_64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-linux-x86.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-linux-armhf.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-linux-arm64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-macosx-arm64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-windows-x86.jar;%APP_HOME%\lib\opencv-4.5.5-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-android-arm.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-android-arm64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-android-x86.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-android-x86_64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-ios-arm64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-ios-x86_64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-linux-x86.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-linux-armhf.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-linux-arm64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-macosx-arm64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-windows-x86.jar;%APP_HOME%\lib\openblas-0.3.19-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-android-arm.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-android-arm64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-android-x86.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-android-x86_64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-linux-x86.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-linux-armhf.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-linux-arm64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-macosx-arm64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-windows-x86.jar;%APP_HOME%\lib\ffmpeg-5.0-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7-linux-x86.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7-linux-armhf.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7-linux-arm64.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7-windows-x86.jar;%APP_HOME%\lib\flycapture-2.13.3.31-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-linux-x86.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-linux-armhf.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-linux-arm64.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-windows-x86.jar;%APP_HOME%\lib\libdc1394-2.2.6-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-linux-x86.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-linux-armhf.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-linux-arm64.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-windows-x86.jar;%APP_HOME%\lib\libfreenect-0.5.7-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\libfreenect2-0.2.0-1.5.7.jar;%APP_HOME%\lib\libfreenect2-0.2.0-1.5.7-linux-x86.jar;%APP_HOME%\lib\libfreenect2-0.2.0-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\libfreenect2-0.2.0-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\libfreenect2-0.2.0-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-linux-armhf.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-linux-arm64.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-linux-x86.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-windows-x86.jar;%APP_HOME%\lib\librealsense-1.12.4-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-linux-armhf.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-linux-arm64.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-linux-x86.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-windows-x86.jar;%APP_HOME%\lib\librealsense2-2.50.0-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\videoinput-0.200-1.5.7.jar;%APP_HOME%\lib\videoinput-0.200-1.5.7-windows-x86.jar;%APP_HOME%\lib\videoinput-0.200-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-android-arm.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-android-arm64.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-android-x86.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-android-x86_64.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-linux-x86.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-linux-armhf.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-linux-arm64.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-windows-x86.jar;%APP_HOME%\lib\artoolkitplus-2.3.1-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-android-arm.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-android-arm64.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-android-x86.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-android-x86_64.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-linux-x86.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-linux-armhf.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-linux-arm64.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-windows-x86.jar;%APP_HOME%\lib\tesseract-5.0.1-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-android-arm.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-android-arm64.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-android-x86.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-android-x86_64.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-linux-x86.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-linux-armhf.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-linux-arm64.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-windows-x86.jar;%APP_HOME%\lib\leptonica-1.82.0-1.5.7-windows-x86_64.jar;%APP_HOME%\lib\javacpp-platform-1.5.7.jar;%APP_HOME%\lib\javacpp-1.5.7.jar;%APP_HOME%\lib\javacpp-1.5.7-android-arm.jar;%APP_HOME%\lib\javacpp-1.5.7-android-arm64.jar;%APP_HOME%\lib\javacpp-1.5.7-android-x86.jar;%APP_HOME%\lib\javacpp-1.5.7-android-x86_64.jar;%APP_HOME%\lib\javacpp-1.5.7-ios-arm64.jar;%APP_HOME%\lib\javacpp-1.5.7-ios-x86_64.jar;%APP_HOME%\lib\javacpp-1.5.7-linux-armhf.jar;%APP_HOME%\lib\javacpp-1.5.7-linux-arm64.jar;%APP_HOME%\lib\javacpp-1.5.7-linux-ppc64le.jar;%APP_HOME%\lib\javacpp-1.5.7-linux-x86.jar;%APP_HOME%\lib\javacpp-1.5.7-linux-x86_64.jar;%APP_HOME%\lib\javacpp-1.5.7-macosx-arm64.jar;%APP_HOME%\lib\javacpp-1.5.7-macosx-x86_64.jar;%APP_HOME%\lib\javacpp-1.5.7-windows-x86.jar;%APP_HOME%\lib\javacpp-1.5.7-windows-x86_64.jar


@rem Execute Overwatcheat
"%JAVA_EXE%" %DEFAULT_JVM_OPTS% %JAVA_OPTS% %OVERWATCHEAT_OPTS%  -classpath "%CLASSPATH%" org.jire.overwatcheat.Main %*

:end
@rem End local scope for the variables with windows NT shell
if %ERRORLEVEL% equ 0 goto mainEnd

:fail
rem Set variable OVERWATCHEAT_EXIT_CONSOLE if you need the _script_ return code instead of
rem the _cmd.exe /c_ return code!
set EXIT_CODE=%ERRORLEVEL%
if %EXIT_CODE% equ 0 set EXIT_CODE=1
if not ""=="%OVERWATCHEAT_EXIT_CONSOLE%" exit %EXIT_CODE%
exit /b %EXIT_CODE%

:mainEnd
if "%OS%"=="Windows_NT" endlocal

:omega
