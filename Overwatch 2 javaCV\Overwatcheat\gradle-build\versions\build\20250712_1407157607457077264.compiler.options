"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\.gradle\\caches\\7.5.1\\generated-gradle-jars\\gradle-api-7.5.1.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-ant-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-astbuilder-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-console-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-datetime-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-dateutil-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-groovydoc-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-json-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-nio-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-sql-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-templates-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-test-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\groovy-xml-3.0.10.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\javaparser-core-3.17.0.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\kotlin-stdlib-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\kotlin-stdlib-common-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\kotlin-stdlib-jdk7-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\kotlin-stdlib-jdk8-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\kotlin-reflect-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\gradle-installation-beacon-7.5.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\7.5.1\\generated-gradle-jars\\gradle-kotlin-dsl-extensions-7.5.1.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\gradle-kotlin-dsl-7.5.1.jar;C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-7.5.1-bin\\7jzzequgds1hbszbhq3npc5ng\\gradle-7.5.1\\lib\\gradle-kotlin-dsl-tooling-models-7.5.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.6.21\\eeb4d60d75e9ea9c11200d52974e522793b14fba\\kotlin-stdlib-jdk8-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-reflect\\1.6.21\\5dc3574d9b7bebfcb4ec6b10ada4aaa9e140bd0b\\kotlin-reflect-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.6.21\\568c1b78a8e17a4f35b31f0a74e2916095ed74c2\\kotlin-stdlib-jdk7-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.6.21\\11ef67f1900634fd951bad28c53ec957fabbe5b8\\kotlin-stdlib-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-common\\1.6.21\\5e5b55c26dbc80372a920aef60eb774b714559b8\\kotlin-stdlib-common-1.6.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar" "-d" "C:\\Users\\<USER>\\Downloads\\overwatch2C\\Overwatch 2 javaCV\\Overwatcheat\\gradle-build\\versions\\build\\classes\\kotlin\\main" "-jdk-home" "C:\\Program Files\\Java\\jdk-17" "-module-name" "versions" "-no-stdlib" "-api-version" "1.4" "-language-version" "1.4" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-compiler-embeddable\\1.6.21\\5286c5c9b215a1a53cb5fde89ab981be308705b7\\kotlin-scripting-compiler-embeddable-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-sam-with-receiver\\1.6.21\\d05a28647814bad1d9a16abec91c710c827f8f05\\kotlin-sam-with-receiver-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-compiler-impl-embeddable\\1.6.21\\75bf05ec0a03994c84d7589badf2fec26f1196c1\\kotlin-scripting-compiler-impl-embeddable-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-jvm\\1.6.21\\ab52f6af29c00a8a9c89ea2b34b5f4d27960bd3f\\kotlin-scripting-jvm-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-common\\1.6.21\\2e2e17b6e20c9c44f1d76d0df4f8f0b3fcfa944f\\kotlin-scripting-common-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.6.21\\11ef67f1900634fd951bad28c53ec957fabbe5b8\\kotlin-stdlib-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-gradle-plugin-api\\1.6.21\\72164483511fee9af6715b0a3f2db4f075c94577\\kotlin-gradle-plugin-api-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-gradle-plugin-model\\1.6.21\\27f5b68d0b3a021da1574893a1ee17caedaf1431\\kotlin-gradle-plugin-model-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-common\\1.6.21\\5e5b55c26dbc80372a920aef60eb774b714559b8\\kotlin-stdlib-common-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-native-utils\\1.6.21\\83287d8f685fa119850b1ca86c4e3a1fbff07109\\kotlin-native-utils-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-project-model\\1.6.21\\71bf2c27e7ce1d59239f960738c0b3e4e23115f6\\kotlin-project-model-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-script-runtime\\1.6.21\\99c6675100da5d6d5b1c5a1032f27f28008d101b\\kotlin-script-runtime-1.6.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-util-io\\1.6.21\\ea85a6e7a215ec2cb88d0a3825b94c5f1de5b220\\kotlin-util-io-1.6.21.jar" "-P" "plugin:org.jetbrains.kotlin.samWithReceiver:annotation=org.gradle.api.HasImplicitReceiver" "-java-parameters" "-Xjvm-default=all" "-Xjsr305=strict" "-XXLanguage:+DisableCompatibilityModeForNewInference" "-Xsuppress-version-warnings" "C:\\Users\\<USER>\\Downloads\\overwatch2C\\Overwatch 2 javaCV\\Overwatcheat\\gradle-build\\versions\\src\\main\\kotlin\\org\\jire\\overwatcheat\\gradle_build\\versions\\Versions.kt" "C:\\Users\\<USER>\\Downloads\\overwatch2C\\Overwatch 2 javaCV\\Overwatcheat\\gradle-build\\versions\\src\\main\\kotlin\\org\\jire\\overwatcheat\\gradle_build\\versions\\VersionsPlugin.kt" "-jvm-target" "1.8"