# The virtual key code key which activates the aim bot when held.
# When using flicking mode, do not set this to left mouse button.
aim_key=1

# 0 for tracking (e.g. <PERSON>, <PERSON><PERSON>), 1 for flicking (e.g. <PERSON><PERSON><PERSON><PERSON>, <PERSON>)
aim_mode=0

# Your in-game sensitivity.
sensitivity=3

# The number of frames per second to capture the game at.
# The higher this is, the faster frames will be sampled and scanned.
# Essentially, the positional accuracy will increase by raising FPS.
#
# The the best way to reduce CPU usage is to lower this.
fps=120

# The number of milliseconds each aim duration should last.
#
# This essentially lets you control how fast the aim bot will snap,
# with lower values resulting in a more snappy and fast aim.
aim_duration_millis=3

# The base float to use to calculate sleep time multiplier.
#
# The higher this is, the more smooth and less sticky the aiming will be.
aim_duration_multiplier_base=1.0

# The maximum float value to use as a sleep time multiplier.
#
# Use this to create an upper limit based off aim duration.
# i.e., if aim_duration_millis is 3, and aim_duration_multiplier_max is 2.0,
# the maximum sleep time would be 6 milliseconds.
#
# For perfectly consistent sleep time, set this to 1.0
aim_duration_multiplier_max=1

# The maximum number of pixels to move per frame scan.
#
# In flick mode, consider increasing this dramatically, 10+
aim_max_move_pixels=5

# The percentage of how much random jitter can be introduced.
#
# In essence, the higher you make this, the more the possibility that the aim position is off,
# resulting in a more human-like aim.
#
# To completely disable (for perfect accuracy), set to 0.
aim_jitter_percent=0

# The sizes in pixels for the minimum required target width and height.
#
# Lowering this value may help with long-range accuracy, but at the cost of more
# false positives should there be any pixels that match the color set.
aim_min_target_width=8
aim_min_target_height=8

# Overwatcheat scans the center of the screen of these given sizes, in pixels.
#
# Generally 256x256 works well across most resolutions, although very high res (4K+) may need a bigger box.
box_width=256
box_height=256

# Divides the box width/height by this value to use as a maximum possible aim snap.
max_snap_divisor=1.5

# The RGB hex color codes to use as a basis to aim at.
#
# You should prefer magenta since it is a unique color not often used.
target_colors=d521cd,d722cf,d623ce,d722ce,d621cd,ce19ca,d11ccb,d21dca,c818cf,d722cd,d722ce,cd19c9,c617d3,cb17c5,da25d3,ce24cc,d328cc,db32ef,bd15c4,dc5bea,da59eb,d959e9,f444fb,cf1ac9,d422d4,d923cd,e53af2,d321d3,e539f3,e035ed,d822cc,e83df5,d11fd1,d622d0,d21dcc,d429e2,e537ef,d923cd,e136ee,d321d3,e63bf3,d722cf,e036ee,d72ce6,d428e1,d321d3,d21dcc,df34ed,d822cc,e434e6,d43ddf,de30e4,be0dbe,d823d3,c814c4,c20ab7,de1ec1,ca16c6,c30ebe,bb0fbf,c510bf,c10cbc,d21cb6,ca14c5,b80cd1,ae0ea8,bf0ec3,d415c1,bc22b7,d317c4,b1179d,bc0fb4,cc47c7,b834b5,dc2cd9,d727d5,de30da,c834c6

# The amount of tolerance for the difference between the target color and the scanned screen color.
#
# Increasing this tolerance may provide faster and more accuracy while aiming, however going too far
# will result in more false positives!
target_color_tolerance=10

# Checks if window title contains this.
# Usually you want this set to "Overwatch".
#
# If you are color blocked (error 5), you can use
# "Fullscreen Projector" in OBS and here.
window_title_search=desktop

# The native mouse device ID, usually this should be 11.
# The range is 11..20 for mouse IDs, and 1..10 for keyboard IDs.
device_id=11

# The offset in pixels to calculate the delta from.
# This number is specified for 1440p and automatically will scale with your screen resolution.
#
# You can try playing around with this to change where to aim, like moving the y down to do bodyshots.
aim_offset_x=1.00
aim_offset_y=0.83

# Maximum pixel distance to target to shoot when flicking. 
# Setting this too high may cause it to shoot prematurely and miss,
# setting this too low may cause it to never shoot.
flick_shoot_pixels=5

# Time off between flick shots (200 for McCree, 350 for Ashe, 270 for Widow)
flick_pause_duration=50

# Whether or not to use the OpenGL overlay.
enable_overlay=false