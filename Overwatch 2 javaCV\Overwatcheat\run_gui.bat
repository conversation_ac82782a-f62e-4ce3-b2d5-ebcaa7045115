@echo off
title Overwatch 2 Aim Assistant GUI
echo Starting Overwatch 2 Aim Assistant GUI...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo Starting Arabic GUI...
echo.

REM Try to run from compiled classes first
java -Xmx1G -Dfile.encoding=UTF-8 -Djava.awt.headless=false -cp "build/classes/kotlin/main;build/resources/main" org.jire.overwatcheat.gui.SimpleGUI

if %errorlevel% neq 0 (
    echo.
    echo Trying to run from JAR file...
    for %%f in (build\libs\Overwatcheat-*.jar) do (
        java -Xmx1G -Dfile.encoding=UTF-8 -Djava.awt.headless=false -jar "%%f" --gui
    )
)

if %errorlevel% neq 0 (
    echo.
    echo Error starting application
    echo Error code: %errorlevel%
    pause
)
