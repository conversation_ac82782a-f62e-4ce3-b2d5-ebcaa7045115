@echo off
title Overwatch 2 Aim Assistant - Arabic GUI
echo ========================================
echo    مساعد التصويب - Overwatch 2
echo    الواجهة العربية الاحترافية
echo ========================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت على النظام
    echo يرجى تثبيت Java 17 أو أحدث
    pause
    exit /b 1
)

REM Check if JAR file exists
if not exist "build\libs\Overwatcheat-*.jar" (
    echo خطأ: ملف JAR غير موجود
    echo يرجى بناء المشروع أولاً باستخدام: gradlew build
    pause
    exit /b 1
)

echo بدء تشغيل الواجهة العربية...
echo.

REM Run the Arabic GUI
java -Xmx1G -Dfile.encoding=UTF-8 -jar "build\libs\Overwatcheat-4.0.0.jar" --gui

if %errorlevel% neq 0 (
    echo.
    echo خطأ في تشغيل التطبيق
    echo كود الخطأ: %errorlevel%
    pause
) else (
    echo.
    echo تم إغلاق الواجهة بنجاح
)
