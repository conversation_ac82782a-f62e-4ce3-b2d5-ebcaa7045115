/*
 * Free, open-source undetected color cheat for Overwatch!
 * Copyright (C) 2017  <PERSON>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package org.jire.overwatcheat.framegrab

import com.sun.jna.Native
import com.sun.jna.Pointer
import org.jire.overwatcheat.nativelib.User32

object FrameWindowFinder {

    private const val WINDOW_TITLE_BYTES_SIZE = 512

    private var windowTitle: CharSequence? = null

    fun findWindowTitle(windowTitleSearch: String): CharSequence {
        /* First iterate using equals */
        User32.EnumWindows({ hwnd, _ ->
            val windowTitleBytes = ByteArray(WINDOW_TITLE_BYTES_SIZE)
            User32.GetWindowTextA(hwnd.pointer, windowTitleBytes, windowTitleBytes.size)
            val windowTitle = Native.toString(windowTitleBytes).trim { it <= ' ' }
            if (windowTitle.equals(windowTitleSearch, true)) {
                FrameWindowFinder.windowTitle = windowTitle
            }
            true
        }, Pointer.NULL)
        if (windowTitle != null) return windowTitle!!

        /* If we couldn't find that, then try using contains */
        User32.EnumWindows({ hwnd, _ ->
            val windowTitleBytes = ByteArray(WINDOW_TITLE_BYTES_SIZE)
            User32.GetWindowTextA(hwnd.pointer, windowTitleBytes, windowTitleBytes.size)
            val windowTitle = Native.toString(windowTitleBytes).trim { it <= ' ' }
            if (windowTitle.contains(windowTitleSearch)) {
                FrameWindowFinder.windowTitle = windowTitle
            }
            true
        }, Pointer.NULL)
        if (windowTitle == null) throw Exception("Could not find window title with search: \"$windowTitleSearch\"")
        return windowTitle!!
    }

}