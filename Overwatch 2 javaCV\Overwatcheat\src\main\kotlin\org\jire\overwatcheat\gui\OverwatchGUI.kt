/*
 * Overwatch 2 Aim Assistant GUI
 * Arabic Professional Interface for Overwatch Aim Training
 */

package org.jire.overwatcheat.gui

import javafx.application.Application
import javafx.application.Platform
import javafx.geometry.Insets
import javafx.geometry.NodeOrientation
import javafx.geometry.Pos
import javafx.scene.Scene
import javafx.scene.control.*
import javafx.scene.layout.*
import javafx.scene.text.Font
import javafx.scene.text.FontWeight
import javafx.stage.Stage
import org.jire.overwatcheat.gui.controllers.MainController
import org.jire.overwatcheat.gui.models.HeroSettings
import org.jire.overwatcheat.gui.models.OverwatchHero
import org.jire.overwatcheat.gui.components.HeroSelectionPanel
import org.jire.overwatcheat.gui.components.HeroSettingsPanel
import org.jire.overwatcheat.gui.components.GeneralSettingsPanel
import org.jire.overwatcheat.gui.components.AimSettingsPanel
import org.jire.overwatcheat.gui.utils.ArabicFontLoader
import org.jire.overwatcheat.gui.utils.StyleManager

class OverwatchGUI : Application() {
    
    private lateinit var primaryStage: Stage
    private lateinit var mainController: MainController
    private lateinit var heroSelectionPanel: HeroSelectionPanel
    private lateinit var heroSettingsPanel: HeroSettingsPanel
    
    override fun start(primaryStage: Stage) {
        this.primaryStage = primaryStage
        this.mainController = MainController()
        
        setupStage()
        createMainInterface()
    }
    
    private fun setupStage() {
        primaryStage.title = "مساعد التصويب - Overwatch 2"
        primaryStage.isResizable = true
        primaryStage.minWidth = 900.0
        primaryStage.minHeight = 700.0
        
        // تطبيق الخط العربي
        ArabicFontLoader.loadArabicFonts()
        
        primaryStage.setOnCloseRequest { 
            mainController.saveCurrentSettings()
            Platform.exit()
        }
    }
    
    private fun createMainInterface() {
        val root = BorderPane()
        root.nodeOrientation = NodeOrientation.RIGHT_TO_LEFT
        
        // إنشاء شريط القائمة العلوي
        val menuBar = createMenuBar()
        root.top = menuBar
        
        // إنشاء التبويبات الرئيسية
        val tabPane = createMainTabs()
        root.center = tabPane
        
        // إنشاء شريط الحالة السفلي
        val statusBar = createStatusBar()
        root.bottom = statusBar
        
        // تطبيق الستايل
        val scene = Scene(root, 1000.0, 750.0)
        StyleManager.applyArabicStyle(scene)
        
        primaryStage.scene = scene
        primaryStage.show()
    }
    
    private fun createMenuBar(): MenuBar {
        val menuBar = MenuBar()
        
        // قائمة الملف
        val fileMenu = Menu("ملف")
        val newProfile = MenuItem("ملف تعريف جديد")
        val loadProfile = MenuItem("تحميل ملف تعريف")
        val saveProfile = MenuItem("حفظ ملف التعريف")
        val separator1 = SeparatorMenuItem()
        val exit = MenuItem("خروج")
        
        newProfile.setOnAction { mainController.createNewProfile() }
        loadProfile.setOnAction { mainController.loadProfile() }
        saveProfile.setOnAction { mainController.saveProfile() }
        exit.setOnAction { Platform.exit() }
        
        fileMenu.items.addAll(newProfile, loadProfile, saveProfile, separator1, exit)
        
        // قائمة الإعدادات
        val settingsMenu = Menu("إعدادات")
        val resetSettings = MenuItem("إعادة تعيين الإعدادات")
        val importSettings = MenuItem("استيراد إعدادات")
        val exportSettings = MenuItem("تصدير إعدادات")
        
        resetSettings.setOnAction { mainController.resetSettings() }
        importSettings.setOnAction { mainController.importSettings() }
        exportSettings.setOnAction { mainController.exportSettings() }
        
        settingsMenu.items.addAll(resetSettings, importSettings, exportSettings)
        
        // قائمة المساعدة
        val helpMenu = Menu("مساعدة")
        val about = MenuItem("حول البرنامج")
        val userGuide = MenuItem("دليل المستخدم")
        
        about.setOnAction { mainController.showAbout() }
        userGuide.setOnAction { mainController.showUserGuide() }
        
        helpMenu.items.addAll(about, userGuide)
        
        menuBar.menus.addAll(fileMenu, settingsMenu, helpMenu)
        return menuBar
    }
    
    private fun createMainTabs(): TabPane {
        val tabPane = TabPane()
        tabPane.tabClosingPolicy = TabPane.TabClosingPolicy.UNAVAILABLE
        
        // تبويب الشخصيات
        val heroesTab = Tab("الشخصيات")
        heroesTab.content = createHeroesPanel()
        
        // تبويب الإعدادات العامة
        val generalTab = Tab("الإعدادات العامة")
        generalTab.content = createGeneralSettingsPanel()
        
        // تبويب إعدادات التصويب
        val aimTab = Tab("إعدادات التصويب")
        aimTab.content = createAimSettingsPanel()
        
        // تبويب الألوان المستهدفة
        val colorsTab = Tab("الألوان المستهدفة")
        colorsTab.content = createColorSettingsPanel()
        
        // تبويب المعاينة المباشرة
        val previewTab = Tab("المعاينة المباشرة")
        previewTab.content = createPreviewPanel()
        
        tabPane.tabs.addAll(heroesTab, generalTab, aimTab, colorsTab, previewTab)
        return tabPane
    }
    
    private fun createHeroesPanel(): HBox {
        val mainPanel = HBox(15.0)
        mainPanel.padding = Insets(15.0)

        // لوحة اختيار الشخصيات (الجانب الأيمن)
        heroSelectionPanel = HeroSelectionPanel { hero ->
            onHeroSelected(hero)
        }
        heroSelectionPanel.prefWidth = 500.0

        // لوحة إعدادات الشخصية (الجانب الأيسر)
        heroSettingsPanel = HeroSettingsPanel(
            mainController.settingsManager
        ) { settings ->
            onHeroSettingsChanged(settings)
        }
        heroSettingsPanel.prefWidth = 450.0

        mainPanel.children.addAll(heroSelectionPanel, heroSettingsPanel)
        return mainPanel
    }

    private fun onHeroSelected(hero: OverwatchHero) {
        mainController.selectHero(hero)
        heroSettingsPanel.updateHero(hero)
    }

    private fun onHeroSettingsChanged(settings: HeroSettings) {
        // تحديث الإعدادات في الوقت الفعلي
        mainController.updateHeroSettings(settings)
    }

    private fun createHeroesGrid(): GridPane {
        val grid = GridPane()
        grid.hgap = 10.0
        grid.vgap = 10.0
        grid.padding = Insets(10.0)
        
        // سيتم إضافة الشخصيات هنا
        val placeholder = Label("سيتم إضافة شبكة الشخصيات هنا")
        grid.add(placeholder, 0, 0)
        
        return grid
    }
    
    private fun createHeroSettingsPanel(): VBox {
        val panel = VBox(10.0)
        panel.padding = Insets(10.0)
        
        val title = Label("إعدادات الشخصية المختارة")
        title.font = Font.font("Arial", FontWeight.BOLD, 16.0)
        
        panel.children.add(title)
        return panel
    }
    
    private fun createGeneralSettingsPanel(): GeneralSettingsPanel {
        return GeneralSettingsPanel()
    }
    
    private fun createAimSettingsPanel(): AimSettingsPanel {
        return AimSettingsPanel()
    }
    
    private fun createColorSettingsPanel(): VBox {
        val panel = VBox(15.0)
        panel.padding = Insets(20.0)
        
        val title = Label("إعدادات الألوان المستهدفة")
        title.font = Font.font("Arial", FontWeight.BOLD, 18.0)
        
        panel.children.add(title)
        return panel
    }
    
    private fun createPreviewPanel(): VBox {
        val panel = VBox(15.0)
        panel.padding = Insets(20.0)
        
        val title = Label("المعاينة المباشرة")
        title.font = Font.font("Arial", FontWeight.BOLD, 18.0)
        
        panel.children.add(title)
        return panel
    }
    
    private fun createStatusBar(): HBox {
        val statusBar = HBox(10.0)
        statusBar.padding = Insets(5.0, 10.0, 5.0, 10.0)
        statusBar.alignment = Pos.CENTER_LEFT
        
        val statusLabel = Label("جاهز")
        val separator = Separator()
        val connectionStatus = Label("غير متصل")
        
        statusBar.children.addAll(statusLabel, separator, connectionStatus)
        return statusBar
    }
    
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            launch(OverwatchGUI::class.java, *args)
        }
        
        fun launchGUI() {
            Platform.runLater {
                val stage = Stage()
                OverwatchGUI().start(stage)
            }
        }
    }
}
