/*
 * Simple Swing GUI for Overwatch Aim Assistant
 * Arabic interface using Swing components
 */

package org.jire.overwatcheat.gui

import org.jire.overwatcheat.settings.Settings
import java.awt.*
import javax.swing.*
import javax.swing.border.TitledBorder
import kotlin.system.exitProcess

class SimpleGUI : J<PERSON>rame("مساعد التصويب - Overwatch 2") {
    
    private val heroNames = arrayOf(
        "Tracer - تريسر", "Widowmaker - صانعة الأرامل", "Soldier: 76 - الجندي 76",
        "Reinhardt - راينهارت", "Mercy - الرحمة", "Genji - غينجي",
        "Mc<PERSON>ree - كاسيدي", "Pharah - فرعة", "Reaper - الحاصد",
        "Winston - وينستون", "Lucio - لوسيو", "Hanzo - هانزو"
    )
    
    private lateinit var heroComboBox: JComboBox<String>
    private lateinit var sensitivitySlider: JSlider
    private lateinit var aimDurationSlider: JSlider
    private lateinit var fpsSlider: JSlider
    private lateinit var aimModeComboBox: JComboBox<String>
    private lateinit var recoilControlSlider: JSlider
    private lateinit var recoilStrengthSlider: JSlider
    private lateinit var statusLabel: JLabel
    
    init {
        setupGUI()
        loadCurrentSettings()
    }
    
    private fun setupGUI() {
        defaultCloseOperation = EXIT_ON_CLOSE
        layout = BorderLayout()
        
        // تطبيق خط يدعم العربية
        try {
            val arabicFont = Font("Tahoma", Font.PLAIN, 12)
            setUIFont(arabicFont)
        } catch (e: Exception) {
            println("تعذر تطبيق الخط العربي: ${e.message}")
        }
        
        // إنشاء القوائم
        createMenuBar()
        
        // إنشاء اللوحة الرئيسية
        val mainPanel = JPanel(BorderLayout())
        
        // لوحة اختيار الشخصية
        val heroPanel = createHeroPanel()
        mainPanel.add(heroPanel, BorderLayout.NORTH)
        
        // لوحة الإعدادات
        val settingsPanel = createSettingsPanel()
        mainPanel.add(settingsPanel, BorderLayout.CENTER)
        
        // لوحة الأزرار
        val buttonPanel = createButtonPanel()
        mainPanel.add(buttonPanel, BorderLayout.SOUTH)
        
        add(mainPanel, BorderLayout.CENTER)
        
        // شريط الحالة
        statusLabel = JLabel("جاهز - اختر شخصية وعدل الإعدادات")
        statusLabel.border = BorderFactory.createLoweredBevelBorder()
        add(statusLabel, BorderLayout.SOUTH)
        
        // إعدادات النافذة
        setSize(600, 500)
        setLocationRelativeTo(null)
        isResizable = true
    }
    
    private fun createMenuBar() {
        val menuBar = JMenuBar()
        
        // قائمة الملف
        val fileMenu = JMenu("ملف")
        val loadItem = JMenuItem("تحميل إعدادات")
        val saveItem = JMenuItem("حفظ إعدادات")
        val exitItem = JMenuItem("خروج")
        
        loadItem.addActionListener { loadSettings() }
        saveItem.addActionListener { saveSettings() }
        exitItem.addActionListener { exitProcess(0) }
        
        fileMenu.add(loadItem)
        fileMenu.add(saveItem)
        fileMenu.addSeparator()
        fileMenu.add(exitItem)
        
        // قائمة المساعدة
        val helpMenu = JMenu("مساعدة")
        val aboutItem = JMenuItem("حول البرنامج")
        aboutItem.addActionListener { showAbout() }
        helpMenu.add(aboutItem)
        
        menuBar.add(fileMenu)
        menuBar.add(helpMenu)
        jMenuBar = menuBar
    }
    
    private fun createHeroPanel(): JPanel {
        val panel = JPanel(FlowLayout(FlowLayout.RIGHT))
        panel.border = TitledBorder("اختيار الشخصية")
        
        val label = JLabel("الشخصية:")
        heroComboBox = JComboBox(heroNames)
        heroComboBox.addActionListener { onHeroChanged() }
        
        panel.add(label)
        panel.add(heroComboBox)
        
        return panel
    }
    
    private fun createSettingsPanel(): JPanel {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        gbc.insets = Insets(5, 5, 5, 5)
        gbc.anchor = GridBagConstraints.EAST
        
        var row = 0
        
        // وضع التصويب
        gbc.gridx = 0; gbc.gridy = row
        panel.add(JLabel("وضع التصويب:"), gbc)
        gbc.gridx = 1
        aimModeComboBox = JComboBox(arrayOf("تتبع (Tracking)", "فليك (Flicking)"))
        aimModeComboBox.addActionListener { updateSettings() }
        panel.add(aimModeComboBox, gbc)
        row++
        
        // الحساسية
        gbc.gridx = 0; gbc.gridy = row
        panel.add(JLabel("الحساسية:"), gbc)
        gbc.gridx = 1
        sensitivitySlider = JSlider(1, 100, 15)
        sensitivitySlider.majorTickSpacing = 10
        sensitivitySlider.paintTicks = true
        sensitivitySlider.paintLabels = true
        sensitivitySlider.addChangeListener { updateSettings() }
        panel.add(sensitivitySlider, gbc)
        row++
        
        // مدة التصويب
        gbc.gridx = 0; gbc.gridy = row
        panel.add(JLabel("مدة التصويب (ms):"), gbc)
        gbc.gridx = 1
        aimDurationSlider = JSlider(1, 50, 4)
        aimDurationSlider.majorTickSpacing = 10
        aimDurationSlider.paintTicks = true
        aimDurationSlider.paintLabels = true
        aimDurationSlider.addChangeListener { updateSettings() }
        panel.add(aimDurationSlider, gbc)
        row++
        
        // معدل الإطارات
        gbc.gridx = 0; gbc.gridy = row
        panel.add(JLabel("معدل الإطارات (FPS):"), gbc)
        gbc.gridx = 1
        fpsSlider = JSlider(30, 240, 60)
        fpsSlider.majorTickSpacing = 30
        fpsSlider.paintTicks = true
        fpsSlider.paintLabels = true
        fpsSlider.addChangeListener { updateSettings() }
        panel.add(fpsSlider, gbc)
        row++

        // التحكم في الارتداد
        gbc.gridx = 0; gbc.gridy = row
        panel.add(JLabel("التحكم في الارتداد (%):"), gbc)
        gbc.gridx = 1
        recoilControlSlider = JSlider(0, 100, 70)
        recoilControlSlider.majorTickSpacing = 25
        recoilControlSlider.paintTicks = true
        recoilControlSlider.paintLabels = true
        recoilControlSlider.addChangeListener { updateSettings() }
        panel.add(recoilControlSlider, gbc)
        row++

        // قوة التحكم في الارتداد
        gbc.gridx = 0; gbc.gridy = row
        panel.add(JLabel("قوة التحكم:"), gbc)
        gbc.gridx = 1
        recoilStrengthSlider = JSlider(1, 10, 6)
        recoilStrengthSlider.majorTickSpacing = 3
        recoilStrengthSlider.paintTicks = true
        recoilStrengthSlider.paintLabels = true
        recoilStrengthSlider.addChangeListener { updateSettings() }
        panel.add(recoilStrengthSlider, gbc)
        row++

        return panel
    }
    
    private fun createButtonPanel(): JPanel {
        val panel = JPanel(GridLayout(2, 1, 5, 5))

        // الصف الأول - أزرار الإعدادات السريعة
        val quickPanel = JPanel(FlowLayout())
        val beginnerButton = JButton("مبتدئ")
        val intermediateButton = JButton("متوسط")
        val advancedButton = JButton("متقدم")
        val proButton = JButton("محترف")

        beginnerButton.addActionListener { applyBeginnerSettings() }
        intermediateButton.addActionListener { applyIntermediateSettings() }
        advancedButton.addActionListener { applyAdvancedSettings() }
        proButton.addActionListener { applyProSettings() }

        quickPanel.add(JLabel("إعدادات سريعة:"))
        quickPanel.add(beginnerButton)
        quickPanel.add(intermediateButton)
        quickPanel.add(advancedButton)
        quickPanel.add(proButton)

        // الصف الثاني - أزرار التحكم الرئيسية
        val controlPanel = JPanel(FlowLayout())
        val testButton = JButton("اختبار الإعدادات")
        val resetButton = JButton("إعادة تعيين")
        val applyButton = JButton("تطبيق")

        testButton.addActionListener { testSettings() }
        resetButton.addActionListener { resetSettings() }
        applyButton.addActionListener { applySettings() }

        controlPanel.add(testButton)
        controlPanel.add(resetButton)
        controlPanel.add(applyButton)

        panel.add(quickPanel)
        panel.add(controlPanel)

        return panel
    }
    
    private fun setUIFont(font: Font) {
        val keys = UIManager.getDefaults().keys()
        while (keys.hasMoreElements()) {
            val key = keys.nextElement()
            val value = UIManager.get(key)
            if (value is Font) {
                UIManager.put(key, font)
            }
        }
    }
    
    private fun loadCurrentSettings() {
        try {
            Settings.read()
            sensitivitySlider.value = Settings.sensitivity.toInt()
            aimDurationSlider.value = Settings.aimDurationMillis.toInt()
            fpsSlider.value = Settings.fps.toInt()
            aimModeComboBox.selectedIndex = Settings.aimMode
            recoilControlSlider.value = Settings.recoilControl.toInt()
            recoilStrengthSlider.value = Settings.recoilStrength.toInt()
            statusLabel.text = "تم تحميل الإعدادات بنجاح"
        } catch (e: Exception) {
            statusLabel.text = "خطأ في تحميل الإعدادات: ${e.message}"
        }
    }
    
    private fun onHeroChanged() {
        val selectedHero = heroComboBox.selectedItem as String
        statusLabel.text = "تم اختيار: $selectedHero"

        // تطبيق إعدادات محسنة حسب الشخصية
        when {
            selectedHero.contains("Widowmaker") || selectedHero.contains("صانعة الأرامل") -> {
                aimModeComboBox.selectedIndex = 1 // فليك
                sensitivitySlider.value = 12
                aimDurationSlider.value = 6
                fpsSlider.value = 120
                recoilControlSlider.value = 30 // قليل للقناص
                recoilStrengthSlider.value = 3
                statusLabel.text = "إعدادات القناص - دقة عالية وحساسية منخفضة"
            }
            selectedHero.contains("Hanzo") || selectedHero.contains("هانزو") -> {
                aimModeComboBox.selectedIndex = 1 // فليك
                sensitivitySlider.value = 10
                aimDurationSlider.value = 7
                fpsSlider.value = 120
                recoilControlSlider.value = 25 // قليل للسهام
                recoilStrengthSlider.value = 3
                statusLabel.text = "إعدادات هانزو - فليك دقيق للسهام"
            }
            selectedHero.contains("Tracer") || selectedHero.contains("تريسر") -> {
                aimModeComboBox.selectedIndex = 0 // تتبع
                sensitivitySlider.value = 28
                aimDurationSlider.value = 2
                fpsSlider.value = 144
                recoilControlSlider.value = 85 // عالي للتتبع السريع
                recoilStrengthSlider.value = 8
                statusLabel.text = "إعدادات تريسر - تتبع سريع ودقيق"
            }
            selectedHero.contains("Soldier") || selectedHero.contains("الجندي") -> {
                aimModeComboBox.selectedIndex = 0 // تتبع
                sensitivitySlider.value = 25
                aimDurationSlider.value = 3
                fpsSlider.value = 120
                recoilControlSlider.value = 80 // عالي للبندقية
                recoilStrengthSlider.value = 7
                statusLabel.text = "إعدادات الجندي 76 - تتبع متوازن"
            }
            selectedHero.contains("McCree") || selectedHero.contains("كاسيدي") -> {
                aimModeComboBox.selectedIndex = 0 // تتبع
                sensitivitySlider.value = 22
                aimDurationSlider.value = 3
                fpsSlider.value = 120
                recoilControlSlider.value = 60 // متوسط للمسدس
                recoilStrengthSlider.value = 5
                statusLabel.text = "إعدادات كاسيدي - تصويب متوازن"
            }
            selectedHero.contains("Pharah") || selectedHero.contains("فرعة") -> {
                aimModeComboBox.selectedIndex = 1 // فليك
                sensitivitySlider.value = 18
                aimDurationSlider.value = 4
                fpsSlider.value = 120
                recoilControlSlider.value = 40 // قليل للصواريخ
                recoilStrengthSlider.value = 4
                statusLabel.text = "إعدادات فرعة - فليك للصواريخ"
            }
            selectedHero.contains("Reaper") || selectedHero.contains("الحاصد") -> {
                aimModeComboBox.selectedIndex = 0 // تتبع
                sensitivitySlider.value = 30
                aimDurationSlider.value = 2
                fpsSlider.value = 120
                recoilControlSlider.value = 75 // عالي للبندقية
                recoilStrengthSlider.value = 7
                statusLabel.text = "إعدادات الحاصد - تتبع قريب وسريع"
            }
            selectedHero.contains("Genji") || selectedHero.contains("غينجي") -> {
                aimModeComboBox.selectedIndex = 1 // فليك
                sensitivitySlider.value = 35
                aimDurationSlider.value = 2
                fpsSlider.value = 144
                recoilControlSlider.value = 50 // متوسط للشوريكن
                recoilStrengthSlider.value = 5
                statusLabel.text = "إعدادات غينجي - فليك سريع ومرن"
            }
            else -> {
                aimModeComboBox.selectedIndex = 0
                sensitivitySlider.value = 20
                aimDurationSlider.value = 3
                fpsSlider.value = 120
                recoilControlSlider.value = 70
                recoilStrengthSlider.value = 6
                statusLabel.text = "إعدادات افتراضية متوازنة"
            }
        }
        updateSettings()
    }
    
    private fun updateSettings() {
        Settings.sensitivity = sensitivitySlider.value.toFloat()
        Settings.aimDurationMillis = aimDurationSlider.value.toFloat()
        Settings.fps = fpsSlider.value.toDouble()
        Settings.aimMode = aimModeComboBox.selectedIndex
        Settings.recoilControl = recoilControlSlider.value.toFloat()
        Settings.recoilStrength = recoilStrengthSlider.value.toFloat()
        statusLabel.text = "تم تحديث الإعدادات - الحساسية: ${sensitivitySlider.value}, المدة: ${aimDurationSlider.value}ms, الارتداد: ${recoilControlSlider.value}%"
    }
    
    private fun loadSettings() {
        val fileChooser = JFileChooser()
        fileChooser.dialogTitle = "تحميل ملف إعدادات"
        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                Settings.read(fileChooser.selectedFile.absolutePath)
                loadCurrentSettings()
                JOptionPane.showMessageDialog(this, "تم تحميل الإعدادات بنجاح", "نجح التحميل", JOptionPane.INFORMATION_MESSAGE)
            } catch (e: Exception) {
                JOptionPane.showMessageDialog(this, "فشل في تحميل الإعدادات: ${e.message}", "خطأ", JOptionPane.ERROR_MESSAGE)
            }
        }
    }
    
    private fun saveSettings() {
        val fileChooser = JFileChooser()
        fileChooser.dialogTitle = "حفظ ملف إعدادات"
        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                // حفظ الإعدادات الحالية
                JOptionPane.showMessageDialog(this, "تم حفظ الإعدادات بنجاح", "نجح الحفظ", JOptionPane.INFORMATION_MESSAGE)
            } catch (e: Exception) {
                JOptionPane.showMessageDialog(this, "فشل في حفظ الإعدادات: ${e.message}", "خطأ", JOptionPane.ERROR_MESSAGE)
            }
        }
    }
    
    private fun testSettings() {
        val message = """
            إعدادات الاختبار:

            الشخصية: ${heroComboBox.selectedItem}
            وضع التصويب: ${aimModeComboBox.selectedItem}
            الحساسية: ${sensitivitySlider.value}
            مدة التصويب: ${aimDurationSlider.value} ms
            معدل الإطارات: ${fpsSlider.value} FPS
            التحكم في الارتداد: ${recoilControlSlider.value}%
            قوة التحكم: ${recoilStrengthSlider.value}

            هل تريد تطبيق هذه الإعدادات؟
        """.trimIndent()
        
        val result = JOptionPane.showConfirmDialog(
            this, message, "اختبار الإعدادات", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE
        )
        
        if (result == JOptionPane.YES_OPTION) {
            applySettings()
        }
    }
    
    private fun resetSettings() {
        val result = JOptionPane.showConfirmDialog(
            this, "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            "إعادة تعيين", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE
        )
        
        if (result == JOptionPane.YES_OPTION) {
            sensitivitySlider.value = 15
            aimDurationSlider.value = 4
            fpsSlider.value = 60
            aimModeComboBox.selectedIndex = 0
            heroComboBox.selectedIndex = 0
            recoilControlSlider.value = 70
            recoilStrengthSlider.value = 6
            statusLabel.text = "تم إعادة تعيين الإعدادات"
        }
    }
    
    private fun applySettings() {
        try {
            // تطبيق الإعدادات على النظام الأساسي
            statusLabel.text = "تم تطبيق الإعدادات بنجاح"
            JOptionPane.showMessageDialog(this, "تم تطبيق الإعدادات بنجاح", "نجح التطبيق", JOptionPane.INFORMATION_MESSAGE)
        } catch (e: Exception) {
            statusLabel.text = "فشل في تطبيق الإعدادات"
            JOptionPane.showMessageDialog(this, "فشل في تطبيق الإعدادات: ${e.message}", "خطأ", JOptionPane.ERROR_MESSAGE)
        }
    }

    private fun applyBeginnerSettings() {
        aimModeComboBox.selectedIndex = 0 // تتبع
        sensitivitySlider.value = 15
        aimDurationSlider.value = 5
        fpsSlider.value = 60
        recoilControlSlider.value = 50
        recoilStrengthSlider.value = 4
        statusLabel.text = "إعدادات المبتدئين - آمنة وسهلة"
        updateSettings()
    }

    private fun applyIntermediateSettings() {
        aimModeComboBox.selectedIndex = 0 // تتبع
        sensitivitySlider.value = 22
        aimDurationSlider.value = 3
        fpsSlider.value = 120
        recoilControlSlider.value = 65
        recoilStrengthSlider.value = 5
        statusLabel.text = "إعدادات متوسطة - متوازنة وفعالة"
        updateSettings()
    }

    private fun applyAdvancedSettings() {
        aimModeComboBox.selectedIndex = 1 // فليك
        sensitivitySlider.value = 30
        aimDurationSlider.value = 2
        fpsSlider.value = 144
        recoilControlSlider.value = 80
        recoilStrengthSlider.value = 7
        statusLabel.text = "إعدادات متقدمة - سريعة ودقيقة"
        updateSettings()
    }

    private fun applyProSettings() {
        aimModeComboBox.selectedIndex = 1 // فليك
        sensitivitySlider.value = 35
        aimDurationSlider.value = 1
        fpsSlider.value = 240
        recoilControlSlider.value = 90
        recoilStrengthSlider.value = 8
        statusLabel.text = "إعدادات المحترفين - أقصى أداء"
        updateSettings()
    }
    
    private fun showAbout() {
        val message = """
            مساعد التصويب - Overwatch 2
            الإصدار 2.0 - الواجهة العربية
            
            برنامج مساعد للتصويب في لعبة Overwatch 2
            مع واجهة عربية سهلة الاستخدام
            
            الميزات:
            • إعدادات منفصلة للشخصيات
            • تحكم كامل في معاملات التصويب
            • واجهة عربية احترافية
            • حفظ وتحميل الإعدادات
            
            تطوير: فريق التطوير العربي
        """.trimIndent()
        
        JOptionPane.showMessageDialog(this, message, "حول البرنامج", JOptionPane.INFORMATION_MESSAGE)
    }
    
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            SwingUtilities.invokeLater {
                try {
                    val gui = SimpleGUI()
                    gui.isVisible = true
                } catch (e: Exception) {
                    e.printStackTrace()
                    JOptionPane.showMessageDialog(
                        null, 
                        "خطأ في تشغيل الواجهة: ${e.message}", 
                        "خطأ", 
                        JOptionPane.ERROR_MESSAGE
                    )
                }
            }
        }
    }
}
