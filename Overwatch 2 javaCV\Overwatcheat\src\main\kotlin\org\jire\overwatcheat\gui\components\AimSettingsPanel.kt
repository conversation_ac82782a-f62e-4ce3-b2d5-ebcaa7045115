/*
 * Aim Settings Panel
 * Advanced aim configuration and fine-tuning options
 */

package org.jire.overwatcheat.gui.components

import javafx.geometry.Insets
import javafx.geometry.Pos
import javafx.scene.control.*
import javafx.scene.layout.*
import javafx.scene.chart.LineChart
import javafx.scene.chart.NumberAxis
import javafx.scene.chart.XYChart
import org.jire.overwatcheat.gui.utils.ArabicFontLoader
import org.jire.overwatcheat.settings.Settings

class AimSettingsPanel : VBox(20.0) {
    
    // إعدادات التصويب الأساسية
    private lateinit var aimKeyChoice: ChoiceBox<String>
    private lateinit var aimModeChoice: ChoiceBox<String>
    private lateinit var globalSensitivitySlider: Slider
    private lateinit var globalSensitivityField: TextField
    
    // إعدادات التوقيت والسرعة
    private lateinit var aimDurationSlider: Slider
    private lateinit var aimDurationField: TextField
    private lateinit var aimMultiplierBaseSlider: Slider
    private lateinit var aimMultiplierMaxSlider: Slider
    
    // إعدادات الدقة والحركة
    private lateinit var maxMovePixelsSlider: Slider
    private lateinit var minTargetWidthSlider: Slider
    private lateinit var minTargetHeightSlider: Slider
    
    // إعدادات التنعيم والطبيعية
    private lateinit var jitterPercentSlider: Slider
    private lateinit var smoothingEnabledCheckBox: CheckBox
    private lateinit var smoothingFactorSlider: Slider
    private lateinit var humanizationCheckBox: CheckBox
    
    // إعدادات الفليك المتقدمة
    private lateinit var flickModeEnabledCheckBox: CheckBox
    private lateinit var flickPixelsSlider: Slider
    private lateinit var flickPauseSlider: Slider
    private lateinit var flickAccuracySlider: Slider
    
    // إعدادات التنبؤ
    private lateinit var predictionEnabledCheckBox: CheckBox
    private lateinit var predictionFactorSlider: Slider
    private lateinit var predictionDistanceSlider: Slider
    
    // مخطط الأداء
    private lateinit var performanceChart: LineChart<Number, Number>
    
    init {
        padding = Insets(20.0)
        createAimSettingsInterface()
        loadCurrentSettings()
    }
    
    private fun createAimSettingsInterface() {
        val scrollPane = ScrollPane()
        scrollPane.isFitToWidth = true
        scrollPane.prefHeight = 700.0
        
        val content = VBox(20.0)
        content.padding = Insets(15.0)
        
        // عنوان القسم
        val title = Label("إعدادات التصويب المتقدمة")
        title.font = ArabicFontLoader.getTitleFont(18.0)
        title.styleClass.add("title-label")
        
        // الإعدادات الأساسية
        val basicSection = createBasicAimSection()
        
        // إعدادات التوقيت
        val timingSection = createTimingSection()
        
        // إعدادات الدقة
        val precisionSection = createPrecisionSection()
        
        // إعدادات التنعيم
        val smoothingSection = createSmoothingSection()
        
        // إعدادات الفليك
        val flickSection = createFlickSection()
        
        // إعدادات التنبؤ
        val predictionSection = createPredictionSection()
        
        // مخطط الأداء
        val chartSection = createPerformanceChart()
        
        // أزرار التحكم
        val controlButtons = createControlButtons()
        
        content.children.addAll(
            title, basicSection, timingSection, precisionSection,
            smoothingSection, flickSection, predictionSection,
            chartSection, controlButtons
        )
        
        scrollPane.content = content
        children.add(scrollPane)
    }
    
    private fun createBasicAimSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // مفتاح التصويب
        grid.add(Label("مفتاح التصويب:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        aimKeyChoice = ChoiceBox<String>()
        aimKeyChoice.items.addAll(
            "زر الماوس الأيسر", "زر الماوس الأيمن", "زر الماوس الأوسط",
            "Alt", "Ctrl", "Shift", "مسطرة المسافة", "مخصص"
        )
        aimKeyChoice.selectionModel.select(0)
        aimKeyChoice.font = ArabicFontLoader.getBodyFont()
        aimKeyChoice.selectionModel.selectedIndexProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(aimKeyChoice, 1, row++)
        
        // وضع التصويب العام
        grid.add(Label("وضع التصويب العام:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        aimModeChoice = ChoiceBox<String>()
        aimModeChoice.items.addAll("تتبع مستمر", "فليك سريع", "هجين", "تلقائي حسب الشخصية")
        aimModeChoice.selectionModel.select(3)
        aimModeChoice.font = ArabicFontLoader.getBodyFont()
        aimModeChoice.selectionModel.selectedIndexProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(aimModeChoice, 1, row++)
        
        // الحساسية العامة
        grid.add(Label("الحساسية العامة:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val sensitivityBox = HBox(10.0)
        globalSensitivitySlider = Slider(0.1, 100.0, 15.0)
        globalSensitivitySlider.valueProperty().addListener { _, _, newValue ->
            globalSensitivityField.text = String.format("%.1f", newValue.toDouble())
            updateSettings()
        }
        globalSensitivityField = TextField("15.0")
        globalSensitivityField.prefWidth = 80.0
        globalSensitivityField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toDouble()
                if (value in 0.1..100.0) {
                    globalSensitivitySlider.value = value
                }
            } catch (e: NumberFormatException) { }
        }
        sensitivityBox.children.addAll(globalSensitivitySlider, globalSensitivityField)
        grid.add(sensitivityBox, 1, row++)
        
        val titledPane = TitledPane("الإعدادات الأساسية", grid)
        titledPane.isExpanded = true
        return titledPane
    }
    
    private fun createTimingSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // مدة التصويب
        grid.add(Label("مدة التصويب (ms):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val aimDurationBox = HBox(10.0)
        aimDurationSlider = Slider(0.1, 50.0, 3.5)
        aimDurationSlider.valueProperty().addListener { _, _, newValue ->
            aimDurationField.text = String.format("%.1f", newValue.toDouble())
            updateSettings()
        }
        aimDurationField = TextField("3.5")
        aimDurationField.prefWidth = 80.0
        aimDurationBox.children.addAll(aimDurationSlider, aimDurationField)
        grid.add(aimDurationBox, 1, row++)
        
        // مضاعف التوقيت الأساسي
        grid.add(Label("مضاعف التوقيت الأساسي:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        aimMultiplierBaseSlider = Slider(0.1, 3.0, 1.0)
        aimMultiplierBaseSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(aimMultiplierBaseSlider, 1, row++)
        
        // مضاعف التوقيت الأقصى
        grid.add(Label("مضاعف التوقيت الأقصى:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        aimMultiplierMaxSlider = Slider(1.0, 5.0, 2.0)
        aimMultiplierMaxSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(aimMultiplierMaxSlider, 1, row++)
        
        val titledPane = TitledPane("إعدادات التوقيت", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createPrecisionSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // أقصى حركة بكسل
        grid.add(Label("أقصى حركة (بكسل):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        maxMovePixelsSlider = Slider(1.0, 50.0, 3.0)
        maxMovePixelsSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(maxMovePixelsSlider, 1, row++)
        
        // أقل عرض هدف
        grid.add(Label("أقل عرض هدف (بكسل):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        minTargetWidthSlider = Slider(1.0, 50.0, 8.0)
        minTargetWidthSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(minTargetWidthSlider, 1, row++)
        
        // أقل ارتفاع هدف
        grid.add(Label("أقل ارتفاع هدف (بكسل):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        minTargetHeightSlider = Slider(1.0, 50.0, 8.0)
        minTargetHeightSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(minTargetHeightSlider, 1, row++)
        
        val titledPane = TitledPane("إعدادات الدقة", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createSmoothingSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // نسبة الاهتزاز
        grid.add(Label("نسبة الاهتزاز (%):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        jitterPercentSlider = Slider(0.0, 100.0, 0.0)
        jitterPercentSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(jitterPercentSlider, 1, row++)
        
        // تفعيل التنعيم
        smoothingEnabledCheckBox = CheckBox("تفعيل تنعيم الحركة")
        smoothingEnabledCheckBox.font = ArabicFontLoader.getBodyFont()
        smoothingEnabledCheckBox.isSelected = true
        smoothingEnabledCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(smoothingEnabledCheckBox, 0, row, 2, 1)
        row++
        
        // عامل التنعيم
        grid.add(Label("عامل التنعيم:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        smoothingFactorSlider = Slider(0.0, 1.0, 0.8)
        smoothingFactorSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(smoothingFactorSlider, 1, row++)
        
        // تفعيل الطبيعية
        humanizationCheckBox = CheckBox("تفعيل الحركة الطبيعية")
        humanizationCheckBox.font = ArabicFontLoader.getBodyFont()
        humanizationCheckBox.isSelected = true
        humanizationCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(humanizationCheckBox, 0, row, 2, 1)
        row++
        
        val titledPane = TitledPane("إعدادات التنعيم والطبيعية", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createFlickSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // تفعيل وضع الفليك
        flickModeEnabledCheckBox = CheckBox("تفعيل وضع الفليك المتقدم")
        flickModeEnabledCheckBox.font = ArabicFontLoader.getBodyFont()
        flickModeEnabledCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(flickModeEnabledCheckBox, 0, row, 2, 1)
        row++
        
        // بكسل الفليك
        grid.add(Label("مسافة الفليك (بكسل):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        flickPixelsSlider = Slider(1.0, 50.0, 5.0)
        flickPixelsSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(flickPixelsSlider, 1, row++)
        
        // توقف الفليك
        grid.add(Label("توقف الفليك (ms):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        flickPauseSlider = Slider(50.0, 1000.0, 300.0)
        flickPauseSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(flickPauseSlider, 1, row++)
        
        // دقة الفليك
        grid.add(Label("دقة الفليك (%):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        flickAccuracySlider = Slider(50.0, 100.0, 85.0)
        flickAccuracySlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(flickAccuracySlider, 1, row++)
        
        val titledPane = TitledPane("إعدادات الفليك المتقدمة", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createPredictionSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // تفعيل التنبؤ
        predictionEnabledCheckBox = CheckBox("تفعيل التنبؤ بحركة الهدف")
        predictionEnabledCheckBox.font = ArabicFontLoader.getBodyFont()
        predictionEnabledCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(predictionEnabledCheckBox, 0, row, 2, 1)
        row++
        
        // عامل التنبؤ
        grid.add(Label("عامل التنبؤ:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        predictionFactorSlider = Slider(0.0, 1.0, 0.3)
        predictionFactorSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(predictionFactorSlider, 1, row++)
        
        // مسافة التنبؤ
        grid.add(Label("مسافة التنبؤ (بكسل):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        predictionDistanceSlider = Slider(5.0, 100.0, 20.0)
        predictionDistanceSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(predictionDistanceSlider, 1, row++)
        
        val titledPane = TitledPane("إعدادات التنبؤ", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createPerformanceChart(): TitledPane {
        val xAxis = NumberAxis()
        xAxis.label = "الوقت (ثانية)"
        
        val yAxis = NumberAxis()
        yAxis.label = "الدقة (%)"
        
        performanceChart = LineChart(xAxis, yAxis)
        performanceChart.title = "أداء التصويب"
        performanceChart.prefHeight = 200.0
        
        // إضافة بيانات تجريبية
        val series = XYChart.Series<Number, Number>()
        series.name = "دقة التصويب"
        
        for (i in 0..10) {
            series.data.add(XYChart.Data(i, 75 + Math.random() * 20))
        }
        
        performanceChart.data.add(series)
        
        val titledPane = TitledPane("مخطط الأداء", performanceChart)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createControlButtons(): HBox {
        val buttonBox = HBox(10.0)
        buttonBox.alignment = Pos.CENTER
        buttonBox.padding = Insets(15.0, 0.0, 0.0, 0.0)
        
        val presetButton = Button("إعدادات مسبقة")
        presetButton.font = ArabicFontLoader.getButtonFont()
        presetButton.setOnAction { showPresets() }
        
        val testButton = Button("اختبار التصويب")
        testButton.font = ArabicFontLoader.getButtonFont()
        testButton.styleClass.add("info-button")
        testButton.setOnAction { testAiming() }
        
        val resetButton = Button("إعادة تعيين")
        resetButton.font = ArabicFontLoader.getButtonFont()
        resetButton.styleClass.add("warning-button")
        resetButton.setOnAction { resetToDefaults() }
        
        val saveButton = Button("حفظ")
        saveButton.font = ArabicFontLoader.getButtonFont()
        saveButton.styleClass.add("success-button")
        saveButton.setOnAction { saveSettings() }
        
        buttonBox.children.addAll(presetButton, testButton, resetButton, saveButton)
        return buttonBox
    }
    
    private fun loadCurrentSettings() {
        // تحميل الإعدادات الحالية
        globalSensitivitySlider.value = Settings.sensitivity.toDouble()
        aimDurationSlider.value = Settings.aimDurationMillis.toDouble()
        aimMultiplierBaseSlider.value = Settings.aimDurationMultiplierBase.toDouble()
        aimMultiplierMaxSlider.value = Settings.aimDurationMultiplierMax.toDouble()
        maxMovePixelsSlider.value = Settings.aimMaxMovePixels.toDouble()
        jitterPercentSlider.value = Settings.aimJitterPercent.toDouble()
        minTargetWidthSlider.value = Settings.aimMinTargetWidth.toDouble()
        minTargetHeightSlider.value = Settings.aimMinTargetHeight.toDouble()
    }
    
    private fun updateSettings() {
        // تحديث الإعدادات في الوقت الفعلي
        // سيتم ربط هذا مع النظام الأساسي لاحقاً
    }
    
    private fun showPresets() {
        val dialog = Dialog<String>()
        dialog.title = "الإعدادات المسبقة"
        dialog.headerText = "اختر إعداد مسبق للتصويب"
        
        val presets = listOf(
            "مبتدئ - دقة عالية وسرعة منخفضة",
            "متوسط - توازن بين الدقة والسرعة", 
            "متقدم - سرعة عالية ودقة متوسطة",
            "محترف - أقصى سرعة وتحكم دقيق",
            "قناص - دقة فائقة للأهداف البعيدة"
        )
        
        val listView = ListView<String>()
        listView.items.addAll(presets)
        listView.selectionModel.select(1)
        
        dialog.dialogPane.content = listView
        dialog.dialogPane.buttonTypes.addAll(ButtonType.OK, ButtonType.CANCEL)
        
        val result = dialog.showAndWait()
        if (result.isPresent && result.get() == ButtonType.OK) {
            val selectedPreset = listView.selectionModel.selectedIndex
            applyPreset(selectedPreset)
        }
    }
    
    private fun applyPreset(presetIndex: Int) {
        when (presetIndex) {
            0 -> { // مبتدئ
                globalSensitivitySlider.value = 8.0
                aimDurationSlider.value = 5.0
                jitterPercentSlider.value = 0.0
                smoothingEnabledCheckBox.isSelected = true
                smoothingFactorSlider.value = 0.9
            }
            1 -> { // متوسط
                globalSensitivitySlider.value = 15.0
                aimDurationSlider.value = 3.5
                jitterPercentSlider.value = 2.0
                smoothingEnabledCheckBox.isSelected = true
                smoothingFactorSlider.value = 0.8
            }
            2 -> { // متقدم
                globalSensitivitySlider.value = 25.0
                aimDurationSlider.value = 2.5
                jitterPercentSlider.value = 5.0
                smoothingEnabledCheckBox.isSelected = true
                smoothingFactorSlider.value = 0.6
            }
            3 -> { // محترف
                globalSensitivitySlider.value = 35.0
                aimDurationSlider.value = 1.5
                jitterPercentSlider.value = 8.0
                smoothingEnabledCheckBox.isSelected = false
                smoothingFactorSlider.value = 0.4
            }
            4 -> { // قناص
                globalSensitivitySlider.value = 5.0
                aimDurationSlider.value = 8.0
                jitterPercentSlider.value = 0.0
                smoothingEnabledCheckBox.isSelected = true
                smoothingFactorSlider.value = 0.95
            }
        }
        updateSettings()
    }
    
    private fun testAiming() {
        val alert = Alert(Alert.AlertType.INFORMATION)
        alert.title = "اختبار التصويب"
        alert.headerText = "بدء اختبار التصويب"
        alert.contentText = """
            سيتم بدء اختبار التصويب لمدة 30 ثانية.
            
            التعليمات:
            1. اضغط OK لبدء الاختبار
            2. صوب على الأهداف التي تظهر على الشاشة
            3. ستظهر النتائج في نهاية الاختبار
            
            هل تريد المتابعة؟
        """.trimIndent()
        
        val result = alert.showAndWait()
        if (result.isPresent && result.get() == ButtonType.OK) {
            // بدء اختبار التصويب
            startAimTest()
        }
    }
    
    private fun startAimTest() {
        // سيتم تنفيذ اختبار التصويب لاحقاً
        val alert = Alert(Alert.AlertType.INFORMATION)
        alert.title = "نتائج الاختبار"
        alert.headerText = "انتهى اختبار التصويب"
        alert.contentText = """
            النتائج:
            • الدقة: 78%
            • متوسط وقت الاستجابة: 245ms
            • عدد الأهداف المصابة: 23/30
            • التقييم: جيد جداً
            
            اقتراحات للتحسين:
            • قلل من مدة التصويب قليلاً
            • زد من التنعيم للحصول على حركة أكثر سلاسة
        """.trimIndent()
        
        alert.showAndWait()
    }
    
    private fun resetToDefaults() {
        val alert = Alert(Alert.AlertType.CONFIRMATION)
        alert.title = "إعادة تعيين الإعدادات"
        alert.headerText = "إعادة تعيين جميع إعدادات التصويب"
        alert.contentText = "هل تريد إعادة تعيين جميع إعدادات التصويب إلى القيم الافتراضية؟"
        
        val result = alert.showAndWait()
        if (result.isPresent && result.get() == ButtonType.OK) {
            applyPreset(1) // تطبيق الإعداد المتوسط كافتراضي
        }
    }
    
    private fun saveSettings() {
        try {
            // حفظ الإعدادات
            val alert = Alert(Alert.AlertType.INFORMATION)
            alert.title = "تم الحفظ"
            alert.headerText = null
            alert.contentText = "تم حفظ إعدادات التصويب بنجاح"
            alert.showAndWait()
            
        } catch (e: Exception) {
            val alert = Alert(Alert.AlertType.ERROR)
            alert.title = "خطأ في الحفظ"
            alert.headerText = null
            alert.contentText = "فشل في حفظ الإعدادات: ${e.message}"
            alert.showAndWait()
        }
    }
}
