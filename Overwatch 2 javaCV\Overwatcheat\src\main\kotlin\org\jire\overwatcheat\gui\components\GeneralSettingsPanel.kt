/*
 * General Settings Panel
 * Manages global application settings like FPS, capture box, overlay, etc.
 */

package org.jire.overwatcheat.gui.components

import javafx.geometry.Insets
import javafx.geometry.Pos
import javafx.scene.control.*
import javafx.scene.layout.*
import org.jire.overwatcheat.gui.utils.ArabicFontLoader
import org.jire.overwatcheat.settings.Settings

class GeneralSettingsPanel : VBox(20.0) {
    
    // عناصر التحكم في الإعدادات
    private lateinit var fpsSlider: Slider
    private lateinit var fpsField: TextField
    private lateinit var boxWidthField: TextField
    private lateinit var boxHeightField: TextField
    private lateinit var maxSnapDivisorSlider: Slider
    private lateinit var maxSnapDivisorField: TextField
    private lateinit var windowTitleField: TextField
    private lateinit var deviceIdChoice: ChoiceBox<String>
    private lateinit var enableOverlayCheckBox: CheckBox
    
    // إعدادات الشاشة
    private lateinit var screenWidthLabel: Label
    private lateinit var screenHeightLabel: Label
    private lateinit var capturePreviewCheckBox: CheckBox
    
    // إعدادات الأداء
    private lateinit var cpuOptimizationCheckBox: CheckBox
    private lateinit var memoryOptimizationCheckBox: CheckBox
    private lateinit var priorityChoice: ChoiceBox<String>
    
    init {
        padding = Insets(20.0)
        createGeneralSettingsInterface()
        loadCurrentSettings()
    }
    
    private fun createGeneralSettingsInterface() {
        val scrollPane = ScrollPane()
        scrollPane.isFitToWidth = true
        scrollPane.prefHeight = 600.0
        
        val content = VBox(20.0)
        content.padding = Insets(15.0)
        
        // عنوان القسم
        val title = Label("الإعدادات العامة")
        title.font = ArabicFontLoader.getTitleFont(18.0)
        title.styleClass.add("title-label")
        
        // إعدادات الالتقاط
        val captureSection = createCaptureSection()
        
        // إعدادات الشاشة
        val displaySection = createDisplaySection()
        
        // إعدادات الأداء
        val performanceSection = createPerformanceSection()
        
        // إعدادات النظام
        val systemSection = createSystemSection()
        
        // أزرار التحكم
        val controlButtons = createControlButtons()
        
        content.children.addAll(
            title, captureSection, displaySection, 
            performanceSection, systemSection, controlButtons
        )
        
        scrollPane.content = content
        children.add(scrollPane)
    }
    
    private fun createCaptureSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // معدل الإطارات (FPS)
        grid.add(Label("معدل الإطارات (FPS):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val fpsBox = HBox(10.0)
        fpsSlider = Slider(30.0, 240.0, 60.0)
        fpsSlider.majorTickUnit = 30.0
        fpsSlider.isShowTickMarks = true
        fpsSlider.isShowTickLabels = true
        fpsSlider.valueProperty().addListener { _, _, newValue ->
            fpsField.text = newValue.toInt().toString()
            updateSettings()
        }
        fpsField = TextField("60")
        fpsField.prefWidth = 80.0
        fpsField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toInt()
                if (value in 30..240) {
                    fpsSlider.value = value.toDouble()
                }
            } catch (e: NumberFormatException) { }
        }
        fpsBox.children.addAll(fpsSlider, fpsField)
        grid.add(fpsBox, 1, row++)
        
        // عرض صندوق الالتقاط
        grid.add(Label("عرض صندوق الالتقاط:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        boxWidthField = TextField("256")
        boxWidthField.prefWidth = 100.0
        boxWidthField.textProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(boxWidthField, 1, row++)
        
        // ارتفاع صندوق الالتقاط
        grid.add(Label("ارتفاع صندوق الالتقاط:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        boxHeightField = TextField("256")
        boxHeightField.prefWidth = 100.0
        boxHeightField.textProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(boxHeightField, 1, row++)
        
        // مقسم أقصى انجذاب
        grid.add(Label("مقسم أقصى انجذاب:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val maxSnapBox = HBox(10.0)
        maxSnapDivisorSlider = Slider(1.0, 5.0, 2.0)
        maxSnapDivisorSlider.majorTickUnit = 0.5
        maxSnapDivisorSlider.isShowTickMarks = true
        maxSnapDivisorSlider.valueProperty().addListener { _, _, newValue ->
            maxSnapDivisorField.text = String.format("%.1f", newValue.toDouble())
            updateSettings()
        }
        maxSnapDivisorField = TextField("2.0")
        maxSnapDivisorField.prefWidth = 80.0
        maxSnapDivisorField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toDouble()
                if (value in 1.0..5.0) {
                    maxSnapDivisorSlider.value = value
                }
            } catch (e: NumberFormatException) { }
        }
        maxSnapBox.children.addAll(maxSnapDivisorSlider, maxSnapDivisorField)
        grid.add(maxSnapBox, 1, row++)
        
        val titledPane = TitledPane("إعدادات الالتقاط", grid)
        titledPane.isExpanded = true
        return titledPane
    }
    
    private fun createDisplaySection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // عنوان النافذة المستهدفة
        grid.add(Label("عنوان النافذة المستهدفة:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        windowTitleField = TextField("Overwatch")
        windowTitleField.prefWidth = 200.0
        windowTitleField.textProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(windowTitleField, 1, row++)
        
        // تفعيل الأوفرلاي
        enableOverlayCheckBox = CheckBox("تفعيل الأوفرلاي")
        enableOverlayCheckBox.font = ArabicFontLoader.getBodyFont()
        enableOverlayCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(enableOverlayCheckBox, 0, row, 2, 1)
        row++
        
        // معاينة منطقة الالتقاط
        capturePreviewCheckBox = CheckBox("معاينة منطقة الالتقاط")
        capturePreviewCheckBox.font = ArabicFontLoader.getBodyFont()
        capturePreviewCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(capturePreviewCheckBox, 0, row, 2, 1)
        row++
        
        // معلومات الشاشة
        grid.add(Label("دقة الشاشة:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val screenInfoBox = HBox(10.0)
        screenWidthLabel = Label("1920")
        screenHeightLabel = Label("1080")
        screenInfoBox.children.addAll(screenWidthLabel, Label("×"), screenHeightLabel)
        grid.add(screenInfoBox, 1, row++)
        
        val titledPane = TitledPane("إعدادات العرض", grid)
        titledPane.isExpanded = true
        return titledPane
    }
    
    private fun createPerformanceSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // أولوية العملية
        grid.add(Label("أولوية العملية:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        priorityChoice = ChoiceBox<String>()
        priorityChoice.items.addAll("منخفضة", "عادية", "عالية", "عالية جداً")
        priorityChoice.selectionModel.select(1) // عادية
        priorityChoice.font = ArabicFontLoader.getBodyFont()
        priorityChoice.selectionModel.selectedIndexProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(priorityChoice, 1, row++)
        
        // تحسين المعالج
        cpuOptimizationCheckBox = CheckBox("تحسين استخدام المعالج")
        cpuOptimizationCheckBox.font = ArabicFontLoader.getBodyFont()
        cpuOptimizationCheckBox.isSelected = true
        cpuOptimizationCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(cpuOptimizationCheckBox, 0, row, 2, 1)
        row++
        
        // تحسين الذاكرة
        memoryOptimizationCheckBox = CheckBox("تحسين استخدام الذاكرة")
        memoryOptimizationCheckBox.font = ArabicFontLoader.getBodyFont()
        memoryOptimizationCheckBox.isSelected = true
        memoryOptimizationCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(memoryOptimizationCheckBox, 0, row, 2, 1)
        row++
        
        val titledPane = TitledPane("إعدادات الأداء", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createSystemSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // معرف الجهاز
        grid.add(Label("معرف جهاز الماوس:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        deviceIdChoice = ChoiceBox<String>()
        for (i in 11..20) {
            deviceIdChoice.items.add("جهاز $i")
        }
        deviceIdChoice.selectionModel.select(0) // جهاز 11
        deviceIdChoice.font = ArabicFontLoader.getBodyFont()
        deviceIdChoice.selectionModel.selectedIndexProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(deviceIdChoice, 1, row++)
        
        // معلومات النظام
        val systemInfoLabel = Label("معلومات النظام:")
        systemInfoLabel.font = ArabicFontLoader.getLabelFont()
        grid.add(systemInfoLabel, 0, row)
        
        val systemInfo = buildString {
            appendLine("نظام التشغيل: ${System.getProperty("os.name")}")
            appendLine("إصدار Java: ${System.getProperty("java.version")}")
            appendLine("الذاكرة المتاحة: ${Runtime.getRuntime().maxMemory() / 1024 / 1024} MB")
        }
        
        val systemInfoArea = TextArea(systemInfo)
        systemInfoArea.isEditable = false
        systemInfoArea.prefRowCount = 3
        systemInfoArea.font = ArabicFontLoader.getBodyFont(11.0)
        grid.add(systemInfoArea, 1, row++)
        
        val titledPane = TitledPane("إعدادات النظام", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createControlButtons(): HBox {
        val buttonBox = HBox(10.0)
        buttonBox.alignment = Pos.CENTER
        buttonBox.padding = Insets(15.0, 0.0, 0.0, 0.0)
        
        val resetButton = Button("إعادة تعيين")
        resetButton.font = ArabicFontLoader.getButtonFont()
        resetButton.styleClass.add("warning-button")
        resetButton.setOnAction { resetToDefaults() }
        
        val testButton = Button("اختبار الإعدادات")
        testButton.font = ArabicFontLoader.getButtonFont()
        testButton.styleClass.add("info-button")
        testButton.setOnAction { testSettings() }
        
        val applyButton = Button("تطبيق")
        applyButton.font = ArabicFontLoader.getButtonFont()
        applyButton.setOnAction { applySettings() }
        
        val saveButton = Button("حفظ")
        saveButton.font = ArabicFontLoader.getButtonFont()
        saveButton.styleClass.add("success-button")
        saveButton.setOnAction { saveSettings() }
        
        buttonBox.children.addAll(resetButton, testButton, applyButton, saveButton)
        return buttonBox
    }
    
    private fun loadCurrentSettings() {
        // تحميل الإعدادات الحالية من Settings
        fpsSlider.value = Settings.fps
        boxWidthField.text = Settings.boxWidth.toString()
        boxHeightField.text = Settings.boxHeight.toString()
        maxSnapDivisorSlider.value = Settings.maxSnapDivisor.toDouble()
        windowTitleField.text = Settings.windowTitleSearch
        deviceIdChoice.selectionModel.select(Settings.deviceId - 11)
        enableOverlayCheckBox.isSelected = Settings.enableOverlay
        
        // تحديث معلومات الشاشة
        updateScreenInfo()
    }
    
    private fun updateScreenInfo() {
        try {
            val screenBounds = javafx.stage.Screen.getPrimary().bounds
            screenWidthLabel.text = screenBounds.width.toInt().toString()
            screenHeightLabel.text = screenBounds.height.toInt().toString()
        } catch (e: Exception) {
            screenWidthLabel.text = "غير معروف"
            screenHeightLabel.text = "غير معروف"
        }
    }
    
    private fun updateSettings() {
        // تحديث الإعدادات في الوقت الفعلي
        try {
            // سيتم ربط هذا مع النظام الأساسي لاحقاً
        } catch (e: Exception) {
            println("خطأ في تحديث الإعدادات: ${e.message}")
        }
    }
    
    private fun resetToDefaults() {
        val alert = Alert(Alert.AlertType.CONFIRMATION)
        alert.title = "إعادة تعيين الإعدادات"
        alert.headerText = "إعادة تعيين جميع الإعدادات العامة"
        alert.contentText = "هل تريد إعادة تعيين جميع الإعدادات العامة إلى القيم الافتراضية؟"
        
        val result = alert.showAndWait()
        if (result.isPresent && result.get() == ButtonType.OK) {
            // إعادة تعيين القيم الافتراضية
            fpsSlider.value = 60.0
            boxWidthField.text = "256"
            boxHeightField.text = "256"
            maxSnapDivisorSlider.value = 2.0
            windowTitleField.text = "Overwatch"
            deviceIdChoice.selectionModel.select(0)
            enableOverlayCheckBox.isSelected = false
            capturePreviewCheckBox.isSelected = false
            cpuOptimizationCheckBox.isSelected = true
            memoryOptimizationCheckBox.isSelected = true
            priorityChoice.selectionModel.select(1)
            
            updateSettings()
        }
    }
    
    private fun testSettings() {
        val alert = Alert(Alert.AlertType.INFORMATION)
        alert.title = "اختبار الإعدادات"
        alert.headerText = "نتائج اختبار الإعدادات"
        
        val testResults = buildString {
            appendLine("✓ معدل الإطارات: ${fpsField.text} FPS")
            appendLine("✓ صندوق الالتقاط: ${boxWidthField.text}×${boxHeightField.text}")
            appendLine("✓ النافذة المستهدفة: ${windowTitleField.text}")
            appendLine("✓ جهاز الماوس: ${deviceIdChoice.value}")
            
            if (enableOverlayCheckBox.isSelected) {
                appendLine("✓ الأوفرلاي مفعل")
            } else {
                appendLine("○ الأوفرلاي معطل")
            }
            
            appendLine("\nجميع الإعدادات صحيحة ويمكن تطبيقها.")
        }
        
        alert.contentText = testResults
        alert.showAndWait()
    }
    
    private fun applySettings() {
        try {
            // تطبيق الإعدادات على النظام الأساسي
            // سيتم ربط هذا مع Settings لاحقاً
            
            val alert = Alert(Alert.AlertType.INFORMATION)
            alert.title = "تم التطبيق"
            alert.headerText = null
            alert.contentText = "تم تطبيق الإعدادات بنجاح"
            alert.showAndWait()
            
        } catch (e: Exception) {
            val alert = Alert(Alert.AlertType.ERROR)
            alert.title = "خطأ في التطبيق"
            alert.headerText = null
            alert.contentText = "فشل في تطبيق الإعدادات: ${e.message}"
            alert.showAndWait()
        }
    }
    
    private fun saveSettings() {
        try {
            // حفظ الإعدادات في ملف التكوين
            // سيتم ربط هذا مع Settings لاحقاً
            
            val alert = Alert(Alert.AlertType.INFORMATION)
            alert.title = "تم الحفظ"
            alert.headerText = null
            alert.contentText = "تم حفظ الإعدادات العامة بنجاح"
            alert.showAndWait()
            
        } catch (e: Exception) {
            val alert = Alert(Alert.AlertType.ERROR)
            alert.title = "خطأ في الحفظ"
            alert.headerText = null
            alert.contentText = "فشل في حفظ الإعدادات: ${e.message}"
            alert.showAndWait()
        }
    }
}
