/*
 * Hero Selection Panel
 * Displays all Overwatch heroes in a grid with role-based organization
 */

package org.jire.overwatcheat.gui.components

import javafx.geometry.Insets
import javafx.geometry.Pos
import javafx.scene.control.*
import javafx.scene.layout.*
import javafx.scene.text.Font
import javafx.scene.text.FontWeight
import org.jire.overwatcheat.gui.models.HeroRole
import org.jire.overwatcheat.gui.models.OverwatchHero
import org.jire.overwatcheat.gui.utils.ArabicFontLoader
import org.jire.overwatcheat.gui.utils.StyleManager

class HeroSelectionPanel(
    private val onHeroSelected: (OverwatchHero) -> Unit
) : VBox(15.0) {
    
    private var selectedHero: OverwatchHero? = null
    private val heroButtons = mutableMapOf<OverwatchHero, Button>()
    private val roleFilters = mutableMapOf<HeroRole, CheckBox>()
    private var searchField: TextField? = null
    
    init {
        padding = Insets(20.0)
        createHeroSelectionInterface()
    }
    
    private fun createHeroSelectionInterface() {
        // عنوان القسم
        val title = Label("اختيار الشخصية")
        title.font = ArabicFontLoader.getTitleFont(20.0)
        title.styleClass.add("title-label")
        
        // شريط البحث والفلاتر
        val searchAndFilters = createSearchAndFilters()
        
        // شبكة الأبطال
        val heroesGrid = createHeroesGrid()
        
        // معلومات البطل المختار
        val heroInfo = createHeroInfoPanel()
        
        children.addAll(title, searchAndFilters, heroesGrid, heroInfo)
    }
    
    private fun createSearchAndFilters(): VBox {
        val container = VBox(10.0)
        
        // شريط البحث
        val searchContainer = HBox(10.0)
        searchContainer.alignment = Pos.CENTER_RIGHT
        
        val searchLabel = Label("البحث:")
        searchLabel.font = ArabicFontLoader.getLabelFont()
        
        searchField = TextField()
        searchField!!.promptText = "ابحث عن شخصية..."
        searchField!!.font = ArabicFontLoader.getBodyFont()
        searchField!!.textProperty().addListener { _, _, newValue ->
            filterHeroes(newValue)
        }
        
        searchContainer.children.addAll(searchField, searchLabel)
        
        // فلاتر الأدوار
        val rolesContainer = HBox(15.0)
        rolesContainer.alignment = Pos.CENTER_RIGHT
        
        val rolesLabel = Label("الأدوار:")
        rolesLabel.font = ArabicFontLoader.getLabelFont()
        
        HeroRole.values().forEach { role ->
            val checkBox = CheckBox(role.arabicName)
            checkBox.font = ArabicFontLoader.getBodyFont()
            checkBox.isSelected = true
            checkBox.selectedProperty().addListener { _, _, _ ->
                filterHeroesByRole()
            }
            roleFilters[role] = checkBox
            rolesContainer.children.add(checkBox)
        }
        
        rolesContainer.children.add(rolesLabel)
        
        // أزرار إضافية
        val buttonsContainer = HBox(10.0)
        buttonsContainer.alignment = Pos.CENTER_RIGHT
        
        val selectAllRoles = Button("تحديد الكل")
        selectAllRoles.font = ArabicFontLoader.getButtonFont()
        selectAllRoles.setOnAction {
            roleFilters.values.forEach { it.isSelected = true }
        }
        
        val deselectAllRoles = Button("إلغاء تحديد الكل")
        deselectAllRoles.font = ArabicFontLoader.getButtonFont()
        deselectAllRoles.setOnAction {
            roleFilters.values.forEach { it.isSelected = false }
        }
        
        val clearSearch = Button("مسح البحث")
        clearSearch.font = ArabicFontLoader.getButtonFont()
        clearSearch.setOnAction {
            searchField!!.clear()
        }
        
        buttonsContainer.children.addAll(clearSearch, deselectAllRoles, selectAllRoles)
        
        container.children.addAll(searchContainer, rolesContainer, buttonsContainer)
        return container
    }
    
    private fun createHeroesGrid(): ScrollPane {
        val scrollPane = ScrollPane()
        scrollPane.isFitToWidth = true
        scrollPane.prefHeight = 400.0
        
        val mainContainer = VBox(20.0)
        mainContainer.padding = Insets(10.0)
        
        // تنظيم الأبطال حسب الدور
        HeroRole.values().forEach { role ->
            val roleSection = createRoleSection(role)
            mainContainer.children.add(roleSection)
        }
        
        scrollPane.content = mainContainer
        return scrollPane
    }
    
    private fun createRoleSection(role: HeroRole): VBox {
        val section = VBox(10.0)
        
        // عنوان الدور
        val roleTitle = Label("${role.arabicName} (${OverwatchHero.getHeroesByRole(role).size})")
        roleTitle.font = ArabicFontLoader.getTitleFont(16.0)
        roleTitle.styleClass.add("section-label")
        
        // شبكة أبطال الدور
        val heroesGrid = GridPane()
        heroesGrid.hgap = 10.0
        heroesGrid.vgap = 10.0
        heroesGrid.padding = Insets(10.0)
        
        val heroesInRole = OverwatchHero.getHeroesByRole(role)
        val columns = 4 // عدد الأعمدة في الشبكة
        
        heroesInRole.forEachIndexed { index, hero ->
            val heroCard = createHeroCard(hero)
            val row = index / columns
            val col = index % columns
            heroesGrid.add(heroCard, col, row)
        }
        
        section.children.addAll(roleTitle, heroesGrid)
        return section
    }
    
    private fun createHeroCard(hero: OverwatchHero): VBox {
        val card = VBox(8.0)
        card.alignment = Pos.CENTER
        card.padding = Insets(12.0)
        card.prefWidth = 140.0
        card.prefHeight = 100.0
        card.styleClass.add("hero-card")
        
        // اسم البطل
        val nameLabel = Label(hero.arabicName)
        nameLabel.font = ArabicFontLoader.getArabicBoldFont(13.0)
        nameLabel.isWrapText = true
        nameLabel.alignment = Pos.CENTER
        
        // اسم البطل بالإنجليزية
        val englishNameLabel = Label(hero.englishName)
        englishNameLabel.font = ArabicFontLoader.getBodyFont(10.0)
        englishNameLabel.style = "-fx-text-fill: #CCCCCC;"
        
        // دور البطل
        val roleLabel = Label(hero.role.arabicName)
        roleLabel.font = ArabicFontLoader.getBodyFont(10.0)
        roleLabel.style = "-fx-text-fill: ${getRoleColor(hero.role)};"
        
        // وصف مختصر
        val descLabel = Label(hero.description)
        descLabel.font = ArabicFontLoader.getBodyFont(9.0)
        descLabel.isWrapText = true
        descLabel.maxWidth = 120.0
        descLabel.style = "-fx-text-fill: #AAAAAA;"
        
        card.children.addAll(nameLabel, englishNameLabel, roleLabel, descLabel)
        
        // إضافة التفاعل
        card.setOnMouseClicked {
            selectHero(hero)
        }
        
        card.setOnMouseEntered {
            if (selectedHero != hero) {
                card.style = "-fx-background-color: #3D3D3D; -fx-border-color: #FF9800;"
            }
        }
        
        card.setOnMouseExited {
            if (selectedHero != hero) {
                card.style = ""
            }
        }
        
        // حفظ مرجع للبطاقة
        heroButtons[hero] = Button() // placeholder, سنستخدم البطاقة نفسها
        
        return card
    }
    
    private fun getRoleColor(role: HeroRole): String {
        return when (role) {
            HeroRole.TANK -> "#4FC3F7"      // أزرق للدبابة
            HeroRole.DAMAGE -> "#FF5722"    // أحمر للضرر
            HeroRole.SUPPORT -> "#4CAF50"   // أخضر للدعم
        }
    }
    
    private fun createHeroInfoPanel(): VBox {
        val panel = VBox(10.0)
        panel.padding = Insets(15.0)
        panel.style = "-fx-background-color: #2D2D2D; -fx-border-color: #FF6B35; -fx-border-width: 1; -fx-border-radius: 5; -fx-background-radius: 5;"
        
        val title = Label("معلومات الشخصية المختارة")
        title.font = ArabicFontLoader.getTitleFont(16.0)
        title.styleClass.add("subtitle-label")
        
        val infoLabel = Label("اختر شخصية لعرض معلوماتها وإعداداتها")
        infoLabel.font = ArabicFontLoader.getBodyFont()
        infoLabel.isWrapText = true
        
        panel.children.addAll(title, infoLabel)
        return panel
    }
    
    private fun selectHero(hero: OverwatchHero) {
        // إلغاء تحديد البطل السابق
        selectedHero?.let { previousHero ->
            // إعادة تعيين ستايل البطاقة السابقة
            updateHeroCardStyle(previousHero, false)
        }
        
        // تحديد البطل الجديد
        selectedHero = hero
        updateHeroCardStyle(hero, true)
        
        // تحديث معلومات البطل
        updateHeroInfo(hero)
        
        // استدعاء callback
        onHeroSelected(hero)
    }
    
    private fun updateHeroCardStyle(hero: OverwatchHero, selected: Boolean) {
        // البحث عن بطاقة البطل وتحديث ستايلها
        // سيتم تنفيذ هذا عند إنشاء البطاقات
    }
    
    private fun updateHeroInfo(hero: OverwatchHero) {
        // تحديث لوحة معلومات البطل
        val infoPanel = children.lastOrNull() as? VBox
        if (infoPanel != null && infoPanel.children.size >= 2) {
            val infoLabel = infoPanel.children[1] as Label
            
            val info = buildString {
                appendLine("الاسم: ${hero.arabicName}")
                appendLine("الاسم الإنجليزي: ${hero.englishName}")
                appendLine("الدور: ${hero.role.arabicName}")
                appendLine("الوضع الافتراضي: ${if (hero.defaultAimMode == 0) "تتبع" else "فليك"}")
                appendLine("الحساسية الافتراضية: ${hero.defaultSensitivity}")
                appendLine("الوصف: ${hero.description}")
                
                if (hero.requiresPreciseAiming()) {
                    appendLine("⚠️ يتطلب تصويب دقيق")
                }
                
                if (hero.usesFlickAiming()) {
                    appendLine("⚡ يستخدم أسلوب الفليك")
                }
            }
            
            infoLabel.text = info
        }
    }
    
    private fun filterHeroes(searchText: String) {
        val filteredHeroes = if (searchText.isBlank()) {
            OverwatchHero.values().toList()
        } else {
            OverwatchHero.values().filter { hero ->
                hero.arabicName.contains(searchText, ignoreCase = true) ||
                hero.englishName.contains(searchText, ignoreCase = true) ||
                hero.description.contains(searchText, ignoreCase = true)
            }
        }

        updateHeroesDisplay(filteredHeroes)
    }

    private fun filterHeroesByRole() {
        val selectedRoles = roleFilters.filter { it.value.isSelected }.keys
        val searchText = searchField?.text ?: ""

        val filteredHeroes = OverwatchHero.values().filter { hero ->
            val matchesRole = selectedRoles.contains(hero.role)
            val matchesSearch = searchText.isBlank() ||
                hero.arabicName.contains(searchText, ignoreCase = true) ||
                hero.englishName.contains(searchText, ignoreCase = true) ||
                hero.description.contains(searchText, ignoreCase = true)

            matchesRole && matchesSearch
        }

        updateHeroesDisplay(filteredHeroes)
    }

    private fun updateHeroesDisplay(filteredHeroes: List<OverwatchHero>) {
        // إخفاء/إظهار بطاقات الأبطال حسب الفلتر
        // سيتم تنفيذ هذا عند إعادة بناء الشبكة
    }
    
    /**
     * الحصول على البطل المختار حالياً
     */
    fun getSelectedHero(): OverwatchHero? = selectedHero
    
    /**
     * تحديد بطل برمجياً
     */
    fun setSelectedHero(hero: OverwatchHero) {
        selectHero(hero)
    }
    
    /**
     * مسح التحديد
     */
    fun clearSelection() {
        selectedHero?.let { updateHeroCardStyle(it, false) }
        selectedHero = null
        
        // مسح معلومات البطل
        val infoPanel = children.lastOrNull() as? VBox
        if (infoPanel != null && infoPanel.children.size >= 2) {
            val infoLabel = infoPanel.children[1] as Label
            infoLabel.text = "اختر شخصية لعرض معلوماتها وإعداداتها"
        }
    }
}
