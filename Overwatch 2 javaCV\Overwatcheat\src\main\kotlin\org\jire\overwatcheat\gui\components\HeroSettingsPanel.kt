/*
 * Hero Settings Panel
 * Displays and manages settings for individual heroes
 */

package org.jire.overwatcheat.gui.components

import javafx.geometry.Insets
import javafx.geometry.Pos
import javafx.scene.control.*
import javafx.scene.layout.*
import org.jire.overwatcheat.gui.models.HeroSettings
import org.jire.overwatcheat.gui.models.OverwatchHero
import org.jire.overwatcheat.gui.utils.ArabicFontLoader
import org.jire.overwatcheat.gui.utils.SettingsManager

class HeroSettingsPanel(
    private val settingsManager: SettingsManager,
    private val onSettingsChanged: (HeroSettings) -> Unit
) : VBox(15.0) {
    
    private var currentHero: OverwatchHero? = null
    private var currentSettings: HeroSettings? = null
    
    // عناصر التحكم في الإعدادات
    private lateinit var aimModeChoice: ChoiceBox<String>
    private lateinit var sensitivitySlider: Slider
    private lateinit var sensitivityField: TextField
    private lateinit var aimDurationSlider: Slider
    private lateinit var aimDurationField: TextField
    private lateinit var maxMovePixelsSlider: Slider
    private lateinit var maxMovePixelsField: TextField
    private lateinit var jitterSlider: Slider
    private lateinit var jitterField: TextField
    private lateinit var offsetXSlider: Slider
    private lateinit var offsetXField: TextField
    private lateinit var offsetYSlider: Slider
    private lateinit var offsetYField: TextField
    private lateinit var flickPixelsField: TextField
    private lateinit var flickPauseField: TextField
    private lateinit var colorToleranceSlider: Slider
    private lateinit var colorToleranceField: TextField
    private lateinit var smoothingCheckBox: CheckBox
    private lateinit var smoothingSlider: Slider
    private lateinit var predictionCheckBox: CheckBox
    private lateinit var predictionSlider: Slider
    private lateinit var notesArea: TextArea
    
    init {
        padding = Insets(20.0)
        createSettingsInterface()
    }
    
    private fun createSettingsInterface() {
        val scrollPane = ScrollPane()
        scrollPane.isFitToWidth = true
        scrollPane.prefHeight = 600.0
        
        val content = VBox(20.0)
        content.padding = Insets(15.0)
        
        // عنوان القسم
        val title = Label("إعدادات الشخصية")
        title.font = ArabicFontLoader.getTitleFont(18.0)
        title.styleClass.add("title-label")
        
        // معلومات الشخصية الحالية
        val heroInfoSection = createHeroInfoSection()
        
        // إعدادات التصويب الأساسية
        val basicAimSection = createBasicAimSection()
        
        // إعدادات متقدمة
        val advancedSection = createAdvancedSection()
        
        // إعدادات الفليك
        val flickSection = createFlickSection()
        
        // إعدادات الألوان
        val colorSection = createColorSection()
        
        // إعدادات إضافية
        val extraSection = createExtraSection()
        
        // أزرار التحكم
        val controlButtons = createControlButtons()
        
        content.children.addAll(
            title, heroInfoSection, basicAimSection, 
            advancedSection, flickSection, colorSection, 
            extraSection, controlButtons
        )
        
        scrollPane.content = content
        children.add(scrollPane)
    }
    
    private fun createHeroInfoSection(): TitledPane {
        val content = VBox(10.0)
        content.padding = Insets(10.0)
        
        val heroNameLabel = Label("لم يتم اختيار شخصية")
        heroNameLabel.font = ArabicFontLoader.getArabicBoldFont(16.0)
        heroNameLabel.id = "heroNameLabel"
        
        val heroDescLabel = Label("اختر شخصية من القائمة لتعديل إعداداتها")
        heroDescLabel.font = ArabicFontLoader.getBodyFont()
        heroDescLabel.isWrapText = true
        heroDescLabel.id = "heroDescLabel"
        
        content.children.addAll(heroNameLabel, heroDescLabel)
        
        val titledPane = TitledPane("معلومات الشخصية", content)
        titledPane.isCollapsible = false
        return titledPane
    }
    
    private fun createBasicAimSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // وضع التصويب
        grid.add(Label("وضع التصويب:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        aimModeChoice = ChoiceBox<String>()
        aimModeChoice.items.addAll("تتبع (Tracking)", "فليك (Flicking)")
        aimModeChoice.font = ArabicFontLoader.getBodyFont()
        aimModeChoice.selectionModel.selectedIndexProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(aimModeChoice, 1, row++)
        
        // الحساسية
        grid.add(Label("الحساسية:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val sensitivityBox = HBox(10.0)
        sensitivitySlider = Slider(0.1, 100.0, 15.0)
        sensitivitySlider.valueProperty().addListener { _, _, newValue ->
            sensitivityField.text = String.format("%.1f", newValue.toDouble())
            updateSettings()
        }
        sensitivityField = TextField("15.0")
        sensitivityField.prefWidth = 80.0
        sensitivityField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toDouble()
                if (value in 0.1..100.0) {
                    sensitivitySlider.value = value
                }
            } catch (e: NumberFormatException) { }
        }
        sensitivityBox.children.addAll(sensitivitySlider, sensitivityField)
        grid.add(sensitivityBox, 1, row++)
        
        // مدة التصويب
        grid.add(Label("مدة التصويب (ms):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val aimDurationBox = HBox(10.0)
        aimDurationSlider = Slider(0.1, 50.0, 3.5)
        aimDurationSlider.valueProperty().addListener { _, _, newValue ->
            aimDurationField.text = String.format("%.1f", newValue.toDouble())
            updateSettings()
        }
        aimDurationField = TextField("3.5")
        aimDurationField.prefWidth = 80.0
        aimDurationField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toDouble()
                if (value in 0.1..50.0) {
                    aimDurationSlider.value = value
                }
            } catch (e: NumberFormatException) { }
        }
        aimDurationBox.children.addAll(aimDurationSlider, aimDurationField)
        grid.add(aimDurationBox, 1, row++)
        
        // أقصى حركة بكسل
        grid.add(Label("أقصى حركة (بكسل):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val maxMoveBox = HBox(10.0)
        maxMovePixelsSlider = Slider(1.0, 50.0, 3.0)
        maxMovePixelsSlider.valueProperty().addListener { _, _, newValue ->
            maxMovePixelsField.text = newValue.toInt().toString()
            updateSettings()
        }
        maxMovePixelsField = TextField("3")
        maxMovePixelsField.prefWidth = 80.0
        maxMovePixelsField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toInt()
                if (value in 1..50) {
                    maxMovePixelsSlider.value = value.toDouble()
                }
            } catch (e: NumberFormatException) { }
        }
        maxMoveBox.children.addAll(maxMovePixelsSlider, maxMovePixelsField)
        grid.add(maxMoveBox, 1, row++)
        
        val titledPane = TitledPane("إعدادات التصويب الأساسية", grid)
        titledPane.isExpanded = true
        return titledPane
    }
    
    private fun createAdvancedSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // نسبة الاهتزاز
        grid.add(Label("نسبة الاهتزاز (%):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val jitterBox = HBox(10.0)
        jitterSlider = Slider(0.0, 100.0, 0.0)
        jitterSlider.valueProperty().addListener { _, _, newValue ->
            jitterField.text = newValue.toInt().toString()
            updateSettings()
        }
        jitterField = TextField("0")
        jitterField.prefWidth = 80.0
        jitterField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toInt()
                if (value in 0..100) {
                    jitterSlider.value = value.toDouble()
                }
            } catch (e: NumberFormatException) { }
        }
        jitterBox.children.addAll(jitterSlider, jitterField)
        grid.add(jitterBox, 1, row++)
        
        // إزاحة X
        grid.add(Label("إزاحة X:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val offsetXBox = HBox(10.0)
        offsetXSlider = Slider(0.0, 2.0, 1.0)
        offsetXSlider.valueProperty().addListener { _, _, newValue ->
            offsetXField.text = String.format("%.2f", newValue.toDouble())
            updateSettings()
        }
        offsetXField = TextField("1.00")
        offsetXField.prefWidth = 80.0
        offsetXField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toDouble()
                if (value in 0.0..2.0) {
                    offsetXSlider.value = value
                }
            } catch (e: NumberFormatException) { }
        }
        offsetXBox.children.addAll(offsetXSlider, offsetXField)
        grid.add(offsetXBox, 1, row++)
        
        // إزاحة Y
        grid.add(Label("إزاحة Y:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        val offsetYBox = HBox(10.0)
        offsetYSlider = Slider(0.0, 2.0, 0.75)
        offsetYSlider.valueProperty().addListener { _, _, newValue ->
            offsetYField.text = String.format("%.2f", newValue.toDouble())
            updateSettings()
        }
        offsetYField = TextField("0.75")
        offsetYField.prefWidth = 80.0
        offsetYField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toDouble()
                if (value in 0.0..2.0) {
                    offsetYSlider.value = value
                }
            } catch (e: NumberFormatException) { }
        }
        offsetYBox.children.addAll(offsetYSlider, offsetYField)
        grid.add(offsetYBox, 1, row++)
        
        val titledPane = TitledPane("إعدادات متقدمة", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createFlickSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        var row = 0
        
        // بكسل الفليك
        grid.add(Label("بكسل الفليك:").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        flickPixelsField = TextField("5")
        flickPixelsField.prefWidth = 100.0
        flickPixelsField.textProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(flickPixelsField, 1, row++)
        
        // توقف الفليك
        grid.add(Label("توقف الفليك (ms):").apply { font = ArabicFontLoader.getLabelFont() }, 0, row)
        flickPauseField = TextField("300")
        flickPauseField.prefWidth = 100.0
        flickPauseField.textProperty().addListener { _, _, _ -> updateSettings() }
        grid.add(flickPauseField, 1, row++)
        
        val titledPane = TitledPane("إعدادات الفليك", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createColorSection(): TitledPane {
        val grid = GridPane()
        grid.hgap = 15.0
        grid.vgap = 12.0
        grid.padding = Insets(10.0)
        
        // تسامح اللون
        grid.add(Label("تسامح اللون:").apply { font = ArabicFontLoader.getLabelFont() }, 0, 0)
        val colorToleranceBox = HBox(10.0)
        colorToleranceSlider = Slider(0.0, 50.0, 8.0)
        colorToleranceSlider.valueProperty().addListener { _, _, newValue ->
            colorToleranceField.text = newValue.toInt().toString()
            updateSettings()
        }
        colorToleranceField = TextField("8")
        colorToleranceField.prefWidth = 80.0
        colorToleranceField.textProperty().addListener { _, _, newValue ->
            try {
                val value = newValue.toInt()
                if (value in 0..50) {
                    colorToleranceSlider.value = value.toDouble()
                }
            } catch (e: NumberFormatException) { }
        }
        colorToleranceBox.children.addAll(colorToleranceSlider, colorToleranceField)
        grid.add(colorToleranceBox, 1, 0)
        
        val titledPane = TitledPane("إعدادات الألوان", grid)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createExtraSection(): TitledPane {
        val content = VBox(10.0)
        content.padding = Insets(10.0)
        
        // تنعيم الحركة
        smoothingCheckBox = CheckBox("تفعيل تنعيم الحركة")
        smoothingCheckBox.font = ArabicFontLoader.getBodyFont()
        smoothingCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        
        smoothingSlider = Slider(0.0, 1.0, 0.8)
        smoothingSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        
        val smoothingBox = VBox(5.0)
        smoothingBox.children.addAll(smoothingCheckBox, smoothingSlider)
        
        // التنبؤ بالحركة
        predictionCheckBox = CheckBox("تفعيل التنبؤ بالحركة")
        predictionCheckBox.font = ArabicFontLoader.getBodyFont()
        predictionCheckBox.selectedProperty().addListener { _, _, _ -> updateSettings() }
        
        predictionSlider = Slider(0.0, 1.0, 0.3)
        predictionSlider.valueProperty().addListener { _, _, _ -> updateSettings() }
        
        val predictionBox = VBox(5.0)
        predictionBox.children.addAll(predictionCheckBox, predictionSlider)
        
        // ملاحظات
        val notesLabel = Label("ملاحظات:")
        notesLabel.font = ArabicFontLoader.getLabelFont()
        
        notesArea = TextArea()
        notesArea.prefRowCount = 3
        notesArea.isWrapText = true
        notesArea.font = ArabicFontLoader.getBodyFont()
        notesArea.textProperty().addListener { _, _, _ -> updateSettings() }
        
        content.children.addAll(smoothingBox, predictionBox, notesLabel, notesArea)
        
        val titledPane = TitledPane("إعدادات إضافية", content)
        titledPane.isExpanded = false
        return titledPane
    }
    
    private fun createControlButtons(): HBox {
        val buttonBox = HBox(10.0)
        buttonBox.alignment = Pos.CENTER
        buttonBox.padding = Insets(15.0, 0.0, 0.0, 0.0)
        
        val resetButton = Button("إعادة تعيين")
        resetButton.font = ArabicFontLoader.getButtonFont()
        resetButton.styleClass.add("warning-button")
        resetButton.setOnAction { resetToDefaults() }
        
        val copyButton = Button("نسخ الإعدادات")
        copyButton.font = ArabicFontLoader.getButtonFont()
        copyButton.setOnAction { copySettings() }
        
        val pasteButton = Button("لصق الإعدادات")
        pasteButton.font = ArabicFontLoader.getButtonFont()
        pasteButton.setOnAction { pasteSettings() }
        
        val saveButton = Button("حفظ")
        saveButton.font = ArabicFontLoader.getButtonFont()
        saveButton.styleClass.add("success-button")
        saveButton.setOnAction { saveSettings() }
        
        buttonBox.children.addAll(resetButton, copyButton, pasteButton, saveButton)
        return buttonBox
    }
    
    /**
     * تحديث الواجهة عند تغيير الشخصية
     */
    fun updateHero(hero: OverwatchHero) {
        currentHero = hero
        currentSettings = settingsManager.getHeroSettings(hero)
        
        updateHeroInfo()
        loadSettingsToUI()
    }
    
    private fun updateHeroInfo() {
        currentHero?.let { hero ->
            val heroNameLabel = lookup("#heroNameLabel") as? Label
            val heroDescLabel = lookup("#heroDescLabel") as? Label
            
            heroNameLabel?.text = "${hero.arabicName} (${hero.englishName})"
            heroDescLabel?.text = buildString {
                appendLine("الدور: ${hero.role.arabicName}")
                appendLine("الوضع الافتراضي: ${if (hero.defaultAimMode == 0) "تتبع" else "فليك"}")
                appendLine("الحساسية الافتراضية: ${hero.defaultSensitivity}")
                appendLine("الوصف: ${hero.description}")
            }
        }
    }
    
    private fun loadSettingsToUI() {
        currentSettings?.let { settings ->
            aimModeChoice.selectionModel.select(settings.aimMode)
            sensitivitySlider.value = settings.sensitivity.toDouble()
            aimDurationSlider.value = settings.aimDurationMillis.toDouble()
            maxMovePixelsSlider.value = settings.aimMaxMovePixels.toDouble()
            jitterSlider.value = settings.aimJitterPercent.toDouble()
            offsetXSlider.value = settings.aimOffsetX.toDouble()
            offsetYSlider.value = settings.aimOffsetY.toDouble()
            flickPixelsField.text = settings.flickPixels.toString()
            flickPauseField.text = settings.flickPause.toString()
            colorToleranceSlider.value = settings.targetColorTolerance.toDouble()
            smoothingCheckBox.isSelected = settings.enableSmoothing
            smoothingSlider.value = settings.smoothingFactor.toDouble()
            predictionCheckBox.isSelected = settings.enablePrediction
            predictionSlider.value = settings.predictionFactor.toDouble()
            notesArea.text = settings.notes
        }
    }
    
    private fun updateSettings() {
        currentSettings?.let { settings ->
            settings.aimMode = aimModeChoice.selectionModel.selectedIndex
            settings.sensitivity = sensitivitySlider.value.toFloat()
            settings.aimDurationMillis = aimDurationSlider.value.toFloat()
            settings.aimMaxMovePixels = maxMovePixelsSlider.value.toInt()
            settings.aimJitterPercent = jitterSlider.value.toInt()
            settings.aimOffsetX = offsetXSlider.value.toFloat()
            settings.aimOffsetY = offsetYSlider.value.toFloat()
            
            try {
                settings.flickPixels = flickPixelsField.text.toInt()
                settings.flickPause = flickPauseField.text.toInt()
            } catch (e: NumberFormatException) { }
            
            settings.targetColorTolerance = colorToleranceSlider.value.toInt()
            settings.enableSmoothing = smoothingCheckBox.isSelected
            settings.smoothingFactor = smoothingSlider.value.toFloat()
            settings.enablePrediction = predictionCheckBox.isSelected
            settings.predictionFactor = predictionSlider.value.toFloat()
            settings.notes = notesArea.text
            settings.lastModified = System.currentTimeMillis()
            
            onSettingsChanged(settings)
        }
    }
    
    private fun resetToDefaults() {
        currentHero?.let { hero ->
            val defaultSettings = hero.getDefaultSettings()
            currentSettings = defaultSettings
            settingsManager.saveHeroSettings(hero, defaultSettings)
            loadSettingsToUI()
            onSettingsChanged(defaultSettings)
        }
    }
    
    private fun copySettings() {
        // نسخ الإعدادات إلى الحافظة
        currentSettings?.let { settings ->
            // سيتم تنفيذ هذا لاحقاً
        }
    }
    
    private fun pasteSettings() {
        // لصق الإعدادات من الحافظة
        // سيتم تنفيذ هذا لاحقاً
    }
    
    private fun saveSettings() {
        currentHero?.let { hero ->
            currentSettings?.let { settings ->
                settingsManager.saveHeroSettings(hero, settings)
                
                val alert = Alert(Alert.AlertType.INFORMATION)
                alert.title = "تم الحفظ"
                alert.headerText = null
                alert.contentText = "تم حفظ إعدادات ${hero.arabicName} بنجاح"
                alert.showAndWait()
            }
        }
    }
}
