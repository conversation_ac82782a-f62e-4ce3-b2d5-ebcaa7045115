/*
 * Main Controller for Overwatch GUI
 * Handles all main application logic and coordination between components
 */

package org.jire.overwatcheat.gui.controllers

import javafx.scene.control.Alert
import javafx.scene.control.ButtonType
import javafx.stage.FileChooser
import javafx.stage.Stage
import org.jire.overwatcheat.gui.models.HeroSettings
import org.jire.overwatcheat.gui.models.OverwatchHero
import org.jire.overwatcheat.gui.utils.SettingsManager
import org.jire.overwatcheat.settings.Settings
import java.io.File

class MainController {

    val settingsManager = SettingsManager()
    private var currentHero: OverwatchHero? = null
    private var currentHeroSettings: HeroSettings? = null
    
    // إعدادات التطبيق الحالية
    var isAimBotActive = false
        private set
    
    var isPreviewMode = false
        private set
    
    init {
        loadDefaultSettings()
    }
    
    /**
     * تحميل الإعدادات الافتراضية
     */
    private fun loadDefaultSettings() {
        try {
            Settings.read()
            showStatusMessage("تم تحميل الإعدادات بنجاح")
        } catch (e: Exception) {
            showErrorMessage("خطأ في تحميل الإعدادات", "تعذر تحميل الإعدادات الافتراضية: ${e.message}")
        }
    }
    
    /**
     * إنشاء ملف تعريف جديد
     */
    fun createNewProfile() {
        val alert = Alert(Alert.AlertType.CONFIRMATION)
        alert.title = "ملف تعريف جديد"
        alert.headerText = "إنشاء ملف تعريف جديد"
        alert.contentText = "هل تريد إنشاء ملف تعريف جديد؟ سيتم فقدان الإعدادات الحالية غير المحفوظة."
        
        val result = alert.showAndWait()
        if (result.isPresent && result.get() == ButtonType.OK) {
            resetAllSettings()
            showInfoMessage("تم إنشاء ملف تعريف جديد", "تم إنشاء ملف تعريف جديد بنجاح")
        }
    }
    
    /**
     * تحميل ملف تعريف
     */
    fun loadProfile() {
        val fileChooser = FileChooser()
        fileChooser.title = "تحميل ملف تعريف"
        fileChooser.extensionFilters.add(
            FileChooser.ExtensionFilter("ملفات الإعدادات", "*.json", "*.cfg")
        )
        
        val file = fileChooser.showOpenDialog(null)
        if (file != null) {
            try {
                settingsManager.loadProfile(file)
                showInfoMessage("تم التحميل بنجاح", "تم تحميل ملف التعريف بنجاح")
            } catch (e: Exception) {
                showErrorMessage("خطأ في التحميل", "تعذر تحميل ملف التعريف: ${e.message}")
            }
        }
    }
    
    /**
     * حفظ ملف التعريف
     */
    fun saveProfile() {
        val fileChooser = FileChooser()
        fileChooser.title = "حفظ ملف تعريف"
        fileChooser.extensionFilters.add(
            FileChooser.ExtensionFilter("ملفات JSON", "*.json")
        )
        
        val file = fileChooser.showSaveDialog(null)
        if (file != null) {
            try {
                settingsManager.saveProfile(file)
                showInfoMessage("تم الحفظ بنجاح", "تم حفظ ملف التعريف بنجاح")
            } catch (e: Exception) {
                showErrorMessage("خطأ في الحفظ", "تعذر حفظ ملف التعريف: ${e.message}")
            }
        }
    }
    
    /**
     * إعادة تعيين جميع الإعدادات
     */
    fun resetSettings() {
        val alert = Alert(Alert.AlertType.CONFIRMATION)
        alert.title = "إعادة تعيين الإعدادات"
        alert.headerText = "إعادة تعيين جميع الإعدادات"
        alert.contentText = "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"
        
        val result = alert.showAndWait()
        if (result.isPresent && result.get() == ButtonType.OK) {
            resetAllSettings()
            showInfoMessage("تم إعادة التعيين", "تم إعادة تعيين جميع الإعدادات بنجاح")
        }
    }
    
    /**
     * استيراد إعدادات
     */
    fun importSettings() {
        val fileChooser = FileChooser()
        fileChooser.title = "استيراد إعدادات"
        fileChooser.extensionFilters.add(
            FileChooser.ExtensionFilter("ملفات الإعدادات", "*.cfg", "*.json")
        )
        
        val file = fileChooser.showOpenDialog(null)
        if (file != null) {
            try {
                settingsManager.importSettings(file)
                showInfoMessage("تم الاستيراد بنجاح", "تم استيراد الإعدادات بنجاح")
            } catch (e: Exception) {
                showErrorMessage("خطأ في الاستيراد", "تعذر استيراد الإعدادات: ${e.message}")
            }
        }
    }
    
    /**
     * تصدير إعدادات
     */
    fun exportSettings() {
        val fileChooser = FileChooser()
        fileChooser.title = "تصدير إعدادات"
        fileChooser.extensionFilters.add(
            FileChooser.ExtensionFilter("ملفات JSON", "*.json")
        )
        
        val file = fileChooser.showSaveDialog(null)
        if (file != null) {
            try {
                settingsManager.exportSettings(file)
                showInfoMessage("تم التصدير بنجاح", "تم تصدير الإعدادات بنجاح")
            } catch (e: Exception) {
                showErrorMessage("خطأ في التصدير", "تعذر تصدير الإعدادات: ${e.message}")
            }
        }
    }
    
    /**
     * عرض معلومات حول البرنامج
     */
    fun showAbout() {
        val alert = Alert(Alert.AlertType.INFORMATION)
        alert.title = "حول البرنامج"
        alert.headerText = "مساعد التصويب - Overwatch 2"
        alert.contentText = """
            الإصدار: 2.0
            
            واجهة احترافية عربية لتحسين دقة التصويب في Overwatch 2
            
            الميزات:
            • إعدادات منفصلة لكل شخصية
            • تحكم كامل في معاملات التصويب
            • واجهة عربية سهلة الاستخدام
            • معاينة مباشرة للإعدادات
            
            تطوير: فريق التطوير العربي
        """.trimIndent()
        
        alert.showAndWait()
    }
    
    /**
     * عرض دليل المستخدم
     */
    fun showUserGuide() {
        val alert = Alert(Alert.AlertType.INFORMATION)
        alert.title = "دليل المستخدم"
        alert.headerText = "كيفية استخدام البرنامج"
        alert.contentText = """
            خطوات الاستخدام:
            
            1. اختر الشخصية من تبويب "الشخصيات"
            2. اضبط الإعدادات العامة في تبويب "الإعدادات العامة"
            3. اضبط إعدادات التصويب في تبويب "إعدادات التصويب"
            4. اختر الألوان المستهدفة في تبويب "الألوان المستهدفة"
            5. استخدم المعاينة المباشرة لاختبار الإعدادات
            6. احفظ الإعدادات باستخدام قائمة "ملف"
            
            نصائح:
            • استخدم إعدادات مختلفة لكل شخصية
            • اختبر الإعدادات في وضع التدريب أولاً
            • احفظ إعدادات متعددة للمقارنة
        """.trimIndent()
        
        alert.showAndWait()
    }
    
    /**
     * حفظ الإعدادات الحالية
     */
    fun saveCurrentSettings() {
        try {
            settingsManager.saveCurrentSettings()
        } catch (e: Exception) {
            println("خطأ في حفظ الإعدادات: ${e.message}")
        }
    }
    
    /**
     * تغيير الشخصية المختارة
     */
    fun selectHero(hero: OverwatchHero) {
        currentHero = hero
        currentHeroSettings = settingsManager.getHeroSettings(hero)
        settingsManager.saveLastUsedHero(hero)
        showStatusMessage("تم اختيار الشخصية: ${hero.arabicName}")
    }

    /**
     * تحديث إعدادات الشخصية
     */
    fun updateHeroSettings(settings: HeroSettings) {
        currentHeroSettings = settings
        currentHero?.let { hero ->
            settingsManager.saveHeroSettings(hero, settings)
        }
    }
    
    /**
     * تفعيل/إلغاء تفعيل مساعد التصويب
     */
    fun toggleAimBot() {
        isAimBotActive = !isAimBotActive
        val status = if (isAimBotActive) "مفعل" else "معطل"
        showStatusMessage("مساعد التصويب: $status")
    }
    
    /**
     * تفعيل/إلغاء تفعيل وضع المعاينة
     */
    fun togglePreviewMode() {
        isPreviewMode = !isPreviewMode
        val status = if (isPreviewMode) "مفعل" else "معطل"
        showStatusMessage("وضع المعاينة: $status")
    }
    
    // دوال مساعدة للرسائل
    private fun showInfoMessage(title: String, message: String) {
        val alert = Alert(Alert.AlertType.INFORMATION)
        alert.title = title
        alert.headerText = null
        alert.contentText = message
        alert.showAndWait()
    }
    
    private fun showErrorMessage(title: String, message: String) {
        val alert = Alert(Alert.AlertType.ERROR)
        alert.title = title
        alert.headerText = null
        alert.contentText = message
        alert.showAndWait()
    }
    
    private fun showStatusMessage(message: String) {
        // سيتم ربطها بشريط الحالة لاحقاً
        println("حالة: $message")
    }
    
    private fun resetAllSettings() {
        // إعادة تعيين جميع الإعدادات إلى القيم الافتراضية
        settingsManager.resetToDefaults()
        currentHero = null
        currentHeroSettings = null
        isAimBotActive = false
        isPreviewMode = false
    }
}
