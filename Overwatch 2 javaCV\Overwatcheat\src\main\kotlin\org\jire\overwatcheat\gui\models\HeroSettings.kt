/*
 * Hero Settings Data Model
 * Contains all aim settings specific to each Overwatch hero
 */

package org.jire.overwatcheat.gui.models

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import java.io.File

data class HeroSettings(
    // معلومات البطل
    var heroName: String = "",
    var arabicName: String = "",
    
    // إعدادات التصويب الأساسية
    var aimMode: Int = 0, // 0 = tracking, 1 = flicking
    var sensitivity: Float = 15.0f,
    var aimDurationMillis: Float = 3.5f,
    var aimDurationMultiplierBase: Float = 1.0f,
    var aimDurationMultiplierMax: Float = 2.0f,
    
    // إعدادات الحركة والدقة
    var aimMaxMovePixels: Int = 3,
    var aimJitterPercent: Int = 0,
    var aimMinTargetWidth: Int = 8,
    var aimMinTargetHeight: Int = 8,
    
    // إعدادات الإزاحة
    var aimOffsetX: Float = 1.0f,
    var aimOffsetY: Float = 0.75f,
    
    // إعدادات الفليك (للأبطال التي تستخدم الفليك)
    var flickPixels: Int = 5,
    var flickPause: Int = 300,
    
    // إعدادات الألوان المستهدفة
    var targetColors: IntArray = intArrayOf(
        0xd521cd, 0xd722cf, 0xd623ce, 0xd722ce, 0xd621cd, 0xce19ca,
        0xd11ccb, 0xd21dca, 0xc818cf, 0xd722cd, 0xd722ce, 0xcd19c9
    ),
    var targetColorTolerance: Int = 8,
    
    // إعدادات متقدمة
    var enableSmoothing: Boolean = true,
    var smoothingFactor: Float = 0.8f,
    var enablePrediction: Boolean = false,
    var predictionFactor: Float = 0.3f,
    
    // إعدادات خاصة بالبطل
    var customSettings: MutableMap<String, Any> = mutableMapOf(),
    
    // معلومات إضافية
    var lastModified: Long = System.currentTimeMillis(),
    var notes: String = ""
) {
    
    /**
     * نسخ الإعدادات من إعدادات أخرى
     */
    fun copyFrom(other: HeroSettings) {
        this.aimMode = other.aimMode
        this.sensitivity = other.sensitivity
        this.aimDurationMillis = other.aimDurationMillis
        this.aimDurationMultiplierBase = other.aimDurationMultiplierBase
        this.aimDurationMultiplierMax = other.aimDurationMultiplierMax
        this.aimMaxMovePixels = other.aimMaxMovePixels
        this.aimJitterPercent = other.aimJitterPercent
        this.aimMinTargetWidth = other.aimMinTargetWidth
        this.aimMinTargetHeight = other.aimMinTargetHeight
        this.aimOffsetX = other.aimOffsetX
        this.aimOffsetY = other.aimOffsetY
        this.flickPixels = other.flickPixels
        this.flickPause = other.flickPause
        this.targetColors = other.targetColors.copyOf()
        this.targetColorTolerance = other.targetColorTolerance
        this.enableSmoothing = other.enableSmoothing
        this.smoothingFactor = other.smoothingFactor
        this.enablePrediction = other.enablePrediction
        this.predictionFactor = other.predictionFactor
        this.customSettings = other.customSettings.toMutableMap()
        this.lastModified = System.currentTimeMillis()
    }
    
    /**
     * إعادة تعيين الإعدادات إلى القيم الافتراضية
     */
    fun resetToDefaults() {
        aimMode = 0
        sensitivity = 15.0f
        aimDurationMillis = 3.5f
        aimDurationMultiplierBase = 1.0f
        aimDurationMultiplierMax = 2.0f
        aimMaxMovePixels = 3
        aimJitterPercent = 0
        aimMinTargetWidth = 8
        aimMinTargetHeight = 8
        aimOffsetX = 1.0f
        aimOffsetY = 0.75f
        flickPixels = 5
        flickPause = 300
        targetColorTolerance = 8
        enableSmoothing = true
        smoothingFactor = 0.8f
        enablePrediction = false
        predictionFactor = 0.3f
        customSettings.clear()
        lastModified = System.currentTimeMillis()
        notes = ""
    }
    
    /**
     * التحقق من صحة الإعدادات
     */
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        
        if (sensitivity <= 0 || sensitivity > 100) {
            errors.add("الحساسية يجب أن تكون بين 0.1 و 100")
        }
        
        if (aimDurationMillis <= 0 || aimDurationMillis > 50) {
            errors.add("مدة التصويب يجب أن تكون بين 0.1 و 50 ميلي ثانية")
        }
        
        if (aimMaxMovePixels <= 0 || aimMaxMovePixels > 50) {
            errors.add("أقصى حركة بكسل يجب أن تكون بين 1 و 50")
        }
        
        if (aimJitterPercent < 0 || aimJitterPercent > 100) {
            errors.add("نسبة الاهتزاز يجب أن تكون بين 0 و 100")
        }
        
        if (targetColorTolerance < 0 || targetColorTolerance > 50) {
            errors.add("تسامح اللون يجب أن يكون بين 0 و 50")
        }
        
        if (smoothingFactor < 0 || smoothingFactor > 1) {
            errors.add("عامل التنعيم يجب أن يكون بين 0 و 1")
        }
        
        if (predictionFactor < 0 || predictionFactor > 1) {
            errors.add("عامل التنبؤ يجب أن يكون بين 0 و 1")
        }
        
        return errors
    }
    
    /**
     * تطبيق الإعدادات على الإعدادات الأساسية للبرنامج
     */
    fun applyToGlobalSettings() {
        org.jire.overwatcheat.settings.Settings.apply {
            // تطبيق الإعدادات على النظام الأساسي
            // سيتم ربطها مع النظام الأساسي لاحقاً
        }
    }
    
    /**
     * إنشاء نسخة من الإعدادات
     */
    fun copy(): HeroSettings {
        return HeroSettings(
            heroName = this.heroName,
            arabicName = this.arabicName,
            aimMode = this.aimMode,
            sensitivity = this.sensitivity,
            aimDurationMillis = this.aimDurationMillis,
            aimDurationMultiplierBase = this.aimDurationMultiplierBase,
            aimDurationMultiplierMax = this.aimDurationMultiplierMax,
            aimMaxMovePixels = this.aimMaxMovePixels,
            aimJitterPercent = this.aimJitterPercent,
            aimMinTargetWidth = this.aimMinTargetWidth,
            aimMinTargetHeight = this.aimMinTargetHeight,
            aimOffsetX = this.aimOffsetX,
            aimOffsetY = this.aimOffsetY,
            flickPixels = this.flickPixels,
            flickPause = this.flickPause,
            targetColors = this.targetColors.copyOf(),
            targetColorTolerance = this.targetColorTolerance,
            enableSmoothing = this.enableSmoothing,
            smoothingFactor = this.smoothingFactor,
            enablePrediction = this.enablePrediction,
            predictionFactor = this.predictionFactor,
            customSettings = this.customSettings.toMutableMap(),
            lastModified = this.lastModified,
            notes = this.notes
        )
    }
    
    /**
     * تحويل الإعدادات إلى JSON
     */
    fun toJson(): String {
        val gson = GsonBuilder().setPrettyPrinting().create()
        return gson.toJson(this)
    }
    
    /**
     * حفظ الإعدادات في ملف
     */
    fun saveToFile(file: File) {
        file.writeText(toJson())
    }
    
    /**
     * الحصول على وصف مختصر للإعدادات
     */
    fun getSummary(): String {
        val modeText = if (aimMode == 0) "تتبع" else "فليك"
        return "الوضع: $modeText | الحساسية: $sensitivity | المدة: ${aimDurationMillis}ms"
    }
    
    /**
     * مقارنة الإعدادات مع إعدادات أخرى
     */
    fun isDifferentFrom(other: HeroSettings): Boolean {
        return this.aimMode != other.aimMode ||
                this.sensitivity != other.sensitivity ||
                this.aimDurationMillis != other.aimDurationMillis ||
                this.aimMaxMovePixels != other.aimMaxMovePixels ||
                this.aimJitterPercent != other.aimJitterPercent ||
                this.targetColorTolerance != other.targetColorTolerance
    }
    
    companion object {
        /**
         * تحميل الإعدادات من JSON
         */
        fun fromJson(json: String): HeroSettings {
            val gson = Gson()
            return gson.fromJson(json, HeroSettings::class.java)
        }
        
        /**
         * تحميل الإعدادات من ملف
         */
        fun loadFromFile(file: File): HeroSettings {
            return fromJson(file.readText())
        }
        
        /**
         * إنشاء إعدادات افتراضية لبطل معين
         */
        fun createDefaultFor(hero: OverwatchHero): HeroSettings {
            return hero.getDefaultSettings()
        }
        
        /**
         * إنشاء إعدادات فارغة
         */
        fun createEmpty(): HeroSettings {
            return HeroSettings()
        }
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as HeroSettings
        
        return heroName == other.heroName &&
                aimMode == other.aimMode &&
                sensitivity == other.sensitivity &&
                aimDurationMillis == other.aimDurationMillis &&
                aimMaxMovePixels == other.aimMaxMovePixels &&
                aimJitterPercent == other.aimJitterPercent &&
                targetColors.contentEquals(other.targetColors) &&
                targetColorTolerance == other.targetColorTolerance
    }
    
    override fun hashCode(): Int {
        var result = heroName.hashCode()
        result = 31 * result + aimMode
        result = 31 * result + sensitivity.hashCode()
        result = 31 * result + aimDurationMillis.hashCode()
        result = 31 * result + aimMaxMovePixels
        result = 31 * result + aimJitterPercent
        result = 31 * result + targetColors.contentHashCode()
        result = 31 * result + targetColorTolerance
        return result
    }
}
