/*
 * Overwatch Hero Data Model
 * Contains all Overwatch 2 heroes with their Arabic names and categories
 */

package org.jire.overwatcheat.gui.models

enum class HeroRole(val arabicName: String, val englishName: String) {
    TANK("دبابة", "Tank"),
    DAMAGE("ضر<PERSON>", "Damage"),
    SUPPORT("دعم", "Support")
}

enum class OverwatchHero(
    val arabicName: String,
    val englishName: String,
    val role: HeroR<PERSON>,
    val defaultAimMode: Int = 0, // 0 = tracking, 1 = flicking
    val defaultSensitivity: Float = 15.0f,
    val description: String = ""
) {
    
    // Tank Heroes - أبطال الدبابة
    REINHARDT("راينهارت", "Reinhardt", HeroRole.TANK, 0, 12.0f, "محارب بدرع كبير ومطرقة"),
    WINSTON("وينستون", "<PERSON>", HeroRole.TANK, 0, 18.0f, "غوريلا عالم بمدفع كهربائي"),
    ZARYA("زاريا", "<PERSON><PERSON><PERSON>", HeroRole.TANK, 0, 15.0f, "جندية روسية بمدفع جاذبية"),
    ROADHOG("رودهوغ", "Roadhog", HeroRole.TANK, 1, 10.0f, "راكب دراجة نارية بخطاف"),
    DVA("دي.فا", "D.Va", HeroRole.TANK, 0, 20.0f, "لاعبة ألعاب في روبوت قتالي"),
    ORISA("أوريسا", "Orisa", HeroRole.TANK, 0, 16.0f, "روبوت حماية بمدفع رشاش"),
    WRECKING_BALL("كرة التدمير", "Wrecking Ball", HeroRole.TANK, 0, 22.0f, "هامستر في كرة قتالية"),
    SIGMA("سيغما", "Sigma", HeroRole.TANK, 0, 14.0f, "عالم فيزياء يتحكم بالجاذبية"),
    JUNKER_QUEEN("ملكة الخردة", "Junker Queen", HeroRole.TANK, 1, 13.0f, "ملكة محاربة بفأس ومغناطيس"),
    RAMATTRA("راماترا", "Ramattra", HeroRole.TANK, 0, 15.0f, "روبوت أومنيك متحول"),
    MAUGA("ماوغا", "Mauga", HeroRole.TANK, 0, 12.0f, "محارب ساموي بمدفعين"),
    
    // Damage Heroes - أبطال الضرر
    TRACER("تريسر", "Tracer", HeroRole.DAMAGE, 0, 25.0f, "مقاتلة سريعة تتحكم بالزمن"),
    REAPER("الحاصد", "Reaper", HeroRole.DAMAGE, 1, 8.0f, "مقاتل مظلم ببنادق مزدوجة"),
    WIDOWMAKER("صانعة الأرامل", "Widowmaker", HeroRole.DAMAGE, 1, 5.0f, "قناصة فرنسية بسم عنكبوت"),
    PHARAH("فرعة", "Pharah", HeroRole.DAMAGE, 1, 12.0f, "جندية مصرية بصواريخ"),
    MCCREE("كاسيدي", "Cassidy", HeroRole.DAMAGE, 1, 10.0f, "رعاة البقر الأمريكي"),
    JUNKRAT("جانكرات", "Junkrat", HeroRole.DAMAGE, 1, 15.0f, "مجنون متفجرات أسترالي"),
    HANZO("هانزو", "Hanzo", HeroRole.DAMAGE, 1, 8.0f, "رامي سهام ياباني"),
    SOLDIER_76("الجندي 76", "Soldier: 76", HeroRole.DAMAGE, 0, 18.0f, "جندي أمريكي مخضرم"),
    SOMBRA("سومبرا", "Sombra", HeroRole.DAMAGE, 0, 20.0f, "هاكر مكسيكية خفية"),
    GENJI("غينجي", "Genji", HeroRole.DAMAGE, 1, 22.0f, "نينجا سايبورغ ياباني"),
    BASTION("باستيون", "Bastion", HeroRole.DAMAGE, 0, 12.0f, "روبوت قتالي متحول"),
    DOOMFIST("قبضة الموت", "Doomfist", HeroRole.DAMAGE, 1, 15.0f, "مقاتل بقبضة آلية"),
    MEI("مي", "Mei", HeroRole.DAMAGE, 0, 16.0f, "عالمة مناخ صينية بمدفع جليد"),
    TORBJORN("تورب", "Torbjörn", HeroRole.DAMAGE, 1, 14.0f, "مهندس سويدي ببرج دفاعي"),
    SYMMETRA("سيميترا", "Symmetra", HeroRole.DAMAGE, 0, 18.0f, "مهندسة هندية بتقنية الضوء"),
    ASHE("آش", "Ashe", HeroRole.DAMAGE, 1, 7.0f, "زعيمة عصابة ببندقية"),
    ECHO("إيكو", "Echo", HeroRole.DAMAGE, 1, 16.0f, "روبوت ذكي يحاكي الأبطال"),
    SOJOURN("سوجورن", "Sojourn", HeroRole.DAMAGE, 0, 17.0f, "قائدة كندية بمدفع طاقة"),
    VENTURE("فنتشر", "Venture", HeroRole.DAMAGE, 1, 14.0f, "مستكشف بمثقاب"),
    
    // Support Heroes - أبطال الدعم
    MERCY("الرحمة", "Mercy", HeroRole.SUPPORT, 0, 20.0f, "طبيبة سويسرية بعصا علاج"),
    LUCIO("لوسيو", "Lúcio", HeroRole.SUPPORT, 0, 25.0f, "دي جي برازيلي بموسيقى علاجية"),
    ZENYATTA("زينياتا", "Zenyatta", HeroRole.SUPPORT, 1, 12.0f, "راهب روبوت بكرات طاقة"),
    ANA("آنا", "Ana", HeroRole.SUPPORT, 1, 8.0f, "قناصة مصرية بسهام علاجية"),
    BRIGITTE("بريجيت", "Brigitte", HeroRole.SUPPORT, 1, 15.0f, "مهندسة سويدية بدرع ومطرقة"),
    MOIRA("مويرا", "Moira", HeroRole.SUPPORT, 0, 18.0f, "عالمة وراثة أيرلندية"),
    BAPTISTE("باتيست", "Baptiste", HeroRole.SUPPORT, 0, 16.0f, "طبيب هايتي عسكري"),
    KIRIKO("كيريكو", "Kiriko", HeroRole.SUPPORT, 1, 14.0f, "نينجا يابانية شافية"),
    LIFEWEAVER("نساج الحياة", "Lifeweaver", HeroRole.SUPPORT, 0, 17.0f, "عالم نباتات تايلندي"),
    ILLARI("إيلاري", "Illari", HeroRole.SUPPORT, 1, 13.0f, "كاهنة شمس بيروفية"),
    JUNO("جونو", "Juno", HeroRole.SUPPORT, 0, 19.0f, "طبيبة فضاء مريخية");
    
    companion object {
        /**
         * الحصول على جميع الأبطال حسب الدور
         */
        fun getHeroesByRole(role: HeroRole): List<OverwatchHero> {
            return values().filter { it.role == role }
        }
        
        /**
         * البحث عن بطل بالاسم العربي
         */
        fun findByArabicName(name: String): OverwatchHero? {
            return values().find { it.arabicName == name }
        }
        
        /**
         * البحث عن بطل بالاسم الإنجليزي
         */
        fun findByEnglishName(name: String): OverwatchHero? {
            return values().find { it.englishName.equals(name, ignoreCase = true) }
        }
        
        /**
         * الحصول على جميع أسماء الأبطال العربية
         */
        fun getAllArabicNames(): List<String> {
            return values().map { it.arabicName }
        }
        
        /**
         * الحصول على عدد الأبطال في كل دور
         */
        fun getHeroCountByRole(): Map<HeroRole, Int> {
            return values().groupBy { it.role }.mapValues { it.value.size }
        }
    }
    
    /**
     * الحصول على الإعدادات الافتراضية للبطل
     */
    fun getDefaultSettings(): HeroSettings {
        return HeroSettings(
            heroName = this.englishName,
            arabicName = this.arabicName,
            aimMode = this.defaultAimMode,
            sensitivity = this.defaultSensitivity,
            aimDurationMillis = when (this.role) {
                HeroRole.TANK -> 4.0f
                HeroRole.DAMAGE -> when (this.defaultAimMode) {
                    1 -> 2.5f // flicking heroes
                    else -> 3.5f // tracking heroes
                }
                HeroRole.SUPPORT -> 3.0f
            },
            aimMaxMovePixels = when (this.defaultAimMode) {
                1 -> 8 // flicking heroes need more movement
                else -> 3 // tracking heroes need less
            },
            aimJitterPercent = when (this.role) {
                HeroRole.TANK -> 5
                HeroRole.DAMAGE -> 2
                HeroRole.SUPPORT -> 3
            }
        )
    }
    
    /**
     * هل البطل يحتاج إعدادات تصويب دقيقة؟
     */
    fun requiresPreciseAiming(): Boolean {
        return when (this) {
            WIDOWMAKER, ANA, HANZO, ASHE -> true
            else -> false
        }
    }
    
    /**
     * هل البطل يستخدم أسلوب التصويب السريع؟
     */
    fun usesFlickAiming(): Boolean {
        return defaultAimMode == 1
    }
}
