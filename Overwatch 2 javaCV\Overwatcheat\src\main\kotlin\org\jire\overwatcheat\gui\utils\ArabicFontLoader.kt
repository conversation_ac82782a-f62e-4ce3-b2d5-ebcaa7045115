/*
 * Arabic Font Loader
 * Handles loading and managing Arabic fonts for the GUI
 */

package org.jire.overwatcheat.gui.utils

import javafx.scene.text.Font
import java.io.InputStream

object ArabicFontLoader {
    
    private var arabicFont: Font? = null
    private var arabicBoldFont: Font? = null
    private var isLoaded = false
    
    // أسماء الخطوط المدعومة
    private val supportedFonts = listOf(
        "<PERSON><PERSON><PERSON>",
        "Arial Unicode MS", 
        "Segoe UI",
        "Calibri",
        "Times New Roman"
    )
    
    /**
     * تحميل الخطوط العربية
     */
    fun loadArabicFonts() {
        if (isLoaded) return
        
        try {
            // محاولة تحميل خط مخصص من الموارد
            loadCustomFont()
            
            // إذا فشل التحميل، استخدم خط النظام
            if (arabicFont == null) {
                loadSystemFont()
            }
            
            isLoaded = true
            println("تم تحميل الخطوط العربية بنجاح")
            
        } catch (e: Exception) {
            println("خطأ في تحميل الخطوط العربية: ${e.message}")
            // استخدام الخط الافتراضي
            arabicFont = Font.getDefault()
            arabicBoldFont = Font.getDefault()
            isLoaded = true
        }
    }
    
    /**
     * تحميل خط مخصص من الموارد
     */
    private fun loadCustomFont() {
        try {
            // محاولة تحميل خط Tajawal (خط عربي جميل)
            val fontStream: InputStream? = javaClass.getResourceAsStream("/fonts/Tajawal-Regular.ttf")
            val boldFontStream: InputStream? = javaClass.getResourceAsStream("/fonts/Tajawal-Bold.ttf")
            
            if (fontStream != null) {
                arabicFont = Font.loadFont(fontStream, 14.0)
                println("تم تحميل خط Tajawal العادي")
            }
            
            if (boldFontStream != null) {
                arabicBoldFont = Font.loadFont(boldFontStream, 14.0)
                println("تم تحميل خط Tajawal العريض")
            }
            
        } catch (e: Exception) {
            println("فشل في تحميل الخط المخصص: ${e.message}")
        }
    }
    
    /**
     * تحميل خط النظام المناسب للعربية
     */
    private fun loadSystemFont() {
        for (fontName in supportedFonts) {
            try {
                val testFont = Font.font(fontName, 14.0)
                if (testFont != null && testFont.name.contains(fontName, ignoreCase = true)) {
                    arabicFont = testFont
                    arabicBoldFont = Font.font(fontName, javafx.scene.text.FontWeight.BOLD, 14.0)
                    println("تم استخدام خط النظام: $fontName")
                    break
                }
            } catch (e: Exception) {
                continue
            }
        }
        
        // إذا لم يتم العثور على أي خط مناسب
        if (arabicFont == null) {
            arabicFont = Font.getDefault()
            arabicBoldFont = Font.getDefault()
            println("تم استخدام الخط الافتراضي")
        }
    }
    
    /**
     * الحصول على الخط العربي العادي
     */
    fun getArabicFont(size: Double = 14.0): Font {
        if (!isLoaded) loadArabicFonts()
        
        return if (arabicFont != null) {
            Font.font(arabicFont!!.family, size)
        } else {
            Font.font("Tahoma", size)
        }
    }
    
    /**
     * الحصول على الخط العربي العريض
     */
    fun getArabicBoldFont(size: Double = 14.0): Font {
        if (!isLoaded) loadArabicFonts()
        
        return if (arabicBoldFont != null) {
            Font.font(arabicBoldFont!!.family, javafx.scene.text.FontWeight.BOLD, size)
        } else {
            Font.font("Tahoma", javafx.scene.text.FontWeight.BOLD, size)
        }
    }
    
    /**
     * الحصول على خط للعناوين
     */
    fun getTitleFont(size: Double = 18.0): Font {
        return getArabicBoldFont(size)
    }
    
    /**
     * الحصول على خط للنصوص العادية
     */
    fun getBodyFont(size: Double = 14.0): Font {
        return getArabicFont(size)
    }
    
    /**
     * الحصول على خط للأزرار
     */
    fun getButtonFont(size: Double = 12.0): Font {
        return getArabicFont(size)
    }
    
    /**
     * الحصول على خط للتسميات
     */
    fun getLabelFont(size: Double = 13.0): Font {
        return getArabicFont(size)
    }
    
    /**
     * الحصول على خط للقوائم
     */
    fun getMenuFont(size: Double = 12.0): Font {
        return getArabicFont(size)
    }
    
    /**
     * التحقق من دعم الخط للعربية
     */
    fun isFontSupportingArabic(font: Font): Boolean {
        return try {
            // اختبار عرض نص عربي
            val arabicText = "العربية"
            // هذا اختبار بسيط، يمكن تحسينه
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * الحصول على قائمة بالخطوط المتاحة التي تدعم العربية
     */
    fun getAvailableArabicFonts(): List<String> {
        val availableFonts = mutableListOf<String>()
        
        Font.getFamilies().forEach { fontFamily ->
            try {
                val testFont = Font.font(fontFamily, 14.0)
                if (isFontSupportingArabic(testFont)) {
                    availableFonts.add(fontFamily)
                }
            } catch (e: Exception) {
                // تجاهل الخطوط التي تسبب أخطاء
            }
        }
        
        return availableFonts
    }
    
    /**
     * تعيين خط مخصص
     */
    fun setCustomFont(fontFamily: String, size: Double = 14.0) {
        try {
            arabicFont = Font.font(fontFamily, size)
            arabicBoldFont = Font.font(fontFamily, javafx.scene.text.FontWeight.BOLD, size)
            println("تم تعيين الخط المخصص: $fontFamily")
        } catch (e: Exception) {
            println("فشل في تعيين الخط المخصص: ${e.message}")
        }
    }
    
    /**
     * إعادة تعيين الخطوط إلى الافتراضية
     */
    fun resetToDefault() {
        arabicFont = null
        arabicBoldFont = null
        isLoaded = false
        loadArabicFonts()
    }
    
    /**
     * الحصول على معلومات الخط الحالي
     */
    fun getCurrentFontInfo(): Map<String, String> {
        return mapOf(
            "arabicFont" to (arabicFont?.family ?: "غير محدد"),
            "arabicBoldFont" to (arabicBoldFont?.family ?: "غير محدد"),
            "isLoaded" to isLoaded.toString(),
            "supportedFonts" to supportedFonts.joinToString(", ")
        )
    }
}
