/*
 * Settings Manager
 * Handles loading, saving, and managing hero-specific settings
 */

package org.jire.overwatcheat.gui.utils

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import org.jire.overwatcheat.gui.models.HeroSettings
import org.jire.overwatcheat.gui.models.OverwatchHero
import org.jire.overwatcheat.settings.Settings
import java.io.File
import java.io.FileNotFoundException
import java.nio.file.Files
import java.nio.file.Paths

class SettingsManager {
    
    private val gson = GsonBuilder().setPrettyPrinting().create()
    private val heroSettingsMap = mutableMapOf<OverwatchHero, HeroSettings>()
    
    // مجلدات الإعدادات
    private val settingsDir = File("settings")
    private val heroSettingsDir = File(settingsDir, "heroes")
    private val profilesDir = File(settingsDir, "profiles")
    
    // ملفات الإعدادات
    private val globalSettingsFile = File(settingsDir, "global_settings.json")
    private val lastUsedHeroFile = File(settingsDir, "last_used_hero.txt")
    
    init {
        createDirectories()
        loadAllHeroSettings()
    }
    
    /**
     * إنشاء المجلدات المطلوبة
     */
    private fun createDirectories() {
        settingsDir.mkdirs()
        heroSettingsDir.mkdirs()
        profilesDir.mkdirs()
    }
    
    /**
     * تحميل جميع إعدادات الأبطال
     */
    private fun loadAllHeroSettings() {
        OverwatchHero.values().forEach { hero ->
            val settingsFile = File(heroSettingsDir, "${hero.englishName.lowercase()}.json")
            
            val settings = if (settingsFile.exists()) {
                try {
                    HeroSettings.loadFromFile(settingsFile)
                } catch (e: Exception) {
                    println("خطأ في تحميل إعدادات ${hero.arabicName}: ${e.message}")
                    hero.getDefaultSettings()
                }
            } else {
                hero.getDefaultSettings()
            }
            
            heroSettingsMap[hero] = settings
        }
    }
    
    /**
     * الحصول على إعدادات بطل معين
     */
    fun getHeroSettings(hero: OverwatchHero): HeroSettings {
        return heroSettingsMap[hero] ?: hero.getDefaultSettings().also {
            heroSettingsMap[hero] = it
        }
    }
    
    /**
     * حفظ إعدادات بطل معين
     */
    fun saveHeroSettings(hero: OverwatchHero, settings: HeroSettings) {
        heroSettingsMap[hero] = settings
        
        val settingsFile = File(heroSettingsDir, "${hero.englishName.lowercase()}.json")
        try {
            settings.saveToFile(settingsFile)
        } catch (e: Exception) {
            throw Exception("فشل في حفظ إعدادات ${hero.arabicName}: ${e.message}")
        }
    }
    
    /**
     * حفظ جميع إعدادات الأبطال
     */
    fun saveAllHeroSettings() {
        heroSettingsMap.forEach { (hero, settings) ->
            try {
                saveHeroSettings(hero, settings)
            } catch (e: Exception) {
                println("خطأ في حفظ إعدادات ${hero.arabicName}: ${e.message}")
            }
        }
    }
    
    /**
     * إعادة تعيين إعدادات بطل معين
     */
    fun resetHeroSettings(hero: OverwatchHero) {
        val defaultSettings = hero.getDefaultSettings()
        heroSettingsMap[hero] = defaultSettings
        saveHeroSettings(hero, defaultSettings)
    }
    
    /**
     * إعادة تعيين جميع الإعدادات
     */
    fun resetToDefaults() {
        OverwatchHero.values().forEach { hero ->
            resetHeroSettings(hero)
        }
    }
    
    /**
     * نسخ إعدادات من بطل إلى آخر
     */
    fun copyHeroSettings(fromHero: OverwatchHero, toHero: OverwatchHero) {
        val sourceSettings = getHeroSettings(fromHero)
        val targetSettings = sourceSettings.copy()
        targetSettings.heroName = toHero.englishName
        targetSettings.arabicName = toHero.arabicName
        
        saveHeroSettings(toHero, targetSettings)
    }
    
    /**
     * تصدير إعدادات بطل معين
     */
    fun exportHeroSettings(hero: OverwatchHero, file: File) {
        val settings = getHeroSettings(hero)
        settings.saveToFile(file)
    }
    
    /**
     * استيراد إعدادات لبطل معين
     */
    fun importHeroSettings(hero: OverwatchHero, file: File) {
        val settings = HeroSettings.loadFromFile(file)
        settings.heroName = hero.englishName
        settings.arabicName = hero.arabicName
        saveHeroSettings(hero, settings)
    }
    
    /**
     * حفظ ملف تعريف كامل
     */
    fun saveProfile(file: File) {
        val profileData = mapOf(
            "heroes" to heroSettingsMap.mapKeys { it.key.englishName },
            "globalSettings" to getCurrentGlobalSettings(),
            "version" to "2.0",
            "createdAt" to System.currentTimeMillis()
        )
        
        file.writeText(gson.toJson(profileData))
    }
    
    /**
     * تحميل ملف تعريف كامل
     */
    fun loadProfile(file: File) {
        if (!file.exists()) {
            throw FileNotFoundException("ملف التعريف غير موجود: ${file.absolutePath}")
        }
        
        try {
            val profileJson = file.readText()
            val profileType = object : TypeToken<Map<String, Any>>() {}.type
            val profileData: Map<String, Any> = gson.fromJson(profileJson, profileType)
            
            // تحميل إعدادات الأبطال
            val heroesData = profileData["heroes"] as? Map<String, Any>
            heroesData?.forEach { (heroName, settingsData) ->
                val hero = OverwatchHero.findByEnglishName(heroName)
                if (hero != null) {
                    val settingsJson = gson.toJson(settingsData)
                    val settings = HeroSettings.fromJson(settingsJson)
                    heroSettingsMap[hero] = settings
                }
            }
            
            // تحميل الإعدادات العامة
            val globalSettings = profileData["globalSettings"] as? Map<String, Any>
            globalSettings?.let { applyGlobalSettings(it) }
            
        } catch (e: Exception) {
            throw Exception("فشل في تحميل ملف التعريف: ${e.message}")
        }
    }
    
    /**
     * تصدير الإعدادات
     */
    fun exportSettings(file: File) {
        saveProfile(file)
    }
    
    /**
     * استيراد الإعدادات
     */
    fun importSettings(file: File) {
        loadProfile(file)
    }
    
    /**
     * حفظ الإعدادات الحالية
     */
    fun saveCurrentSettings() {
        saveAllHeroSettings()
        saveGlobalSettings()
    }
    
    /**
     * حفظ البطل المستخدم أخيراً
     */
    fun saveLastUsedHero(hero: OverwatchHero) {
        try {
            lastUsedHeroFile.writeText(hero.englishName)
        } catch (e: Exception) {
            println("خطأ في حفظ البطل الأخير: ${e.message}")
        }
    }
    
    /**
     * تحميل البطل المستخدم أخيراً
     */
    fun loadLastUsedHero(): OverwatchHero? {
        return try {
            if (lastUsedHeroFile.exists()) {
                val heroName = lastUsedHeroFile.readText().trim()
                OverwatchHero.findByEnglishName(heroName)
            } else {
                null
            }
        } catch (e: Exception) {
            println("خطأ في تحميل البطل الأخير: ${e.message}")
            null
        }
    }
    
    /**
     * الحصول على الإعدادات العامة الحالية
     */
    private fun getCurrentGlobalSettings(): Map<String, Any> {
        return mapOf(
            "fps" to Settings.fps,
            "boxWidth" to Settings.boxWidth,
            "boxHeight" to Settings.boxHeight,
            "maxSnapDivisor" to Settings.maxSnapDivisor,
            "windowTitleSearch" to Settings.windowTitleSearch,
            "deviceId" to Settings.deviceId,
            "enableOverlay" to Settings.enableOverlay
        )
    }
    
    /**
     * تطبيق الإعدادات العامة
     */
    private fun applyGlobalSettings(settings: Map<String, Any>) {
        // سيتم تطبيق الإعدادات على النظام الأساسي
        // هذا يتطلب تعديل كلاس Settings الأساسي
    }
    
    /**
     * حفظ الإعدادات العامة
     */
    private fun saveGlobalSettings() {
        try {
            val globalSettings = getCurrentGlobalSettings()
            globalSettingsFile.writeText(gson.toJson(globalSettings))
        } catch (e: Exception) {
            println("خطأ في حفظ الإعدادات العامة: ${e.message}")
        }
    }
    
    /**
     * الحصول على إحصائيات الإعدادات
     */
    fun getSettingsStatistics(): Map<String, Any> {
        val stats = mutableMapOf<String, Any>()
        
        // عدد الأبطال مع إعدادات مخصصة
        val customizedHeroes = heroSettingsMap.count { (hero, settings) ->
            settings.isDifferentFrom(hero.getDefaultSettings())
        }
        
        stats["totalHeroes"] = OverwatchHero.values().size
        stats["customizedHeroes"] = customizedHeroes
        stats["lastModified"] = heroSettingsMap.values.maxOfOrNull { it.lastModified } ?: 0L
        
        // إحصائيات حسب الدور
        OverwatchHero.values().groupBy { it.role }.forEach { (role, heroes) ->
            val customizedInRole = heroes.count { hero ->
                heroSettingsMap[hero]?.isDifferentFrom(hero.getDefaultSettings()) == true
            }
            stats["${role.englishName.lowercase()}Customized"] = customizedInRole
        }
        
        return stats
    }
    
    /**
     * البحث عن الأبطال بإعدادات مشابهة
     */
    fun findSimilarSettings(targetHero: OverwatchHero): List<OverwatchHero> {
        val targetSettings = getHeroSettings(targetHero)
        
        return OverwatchHero.values().filter { hero ->
            if (hero == targetHero) return@filter false
            
            val heroSettings = getHeroSettings(hero)
            
            // مقارنة الإعدادات الأساسية
            heroSettings.aimMode == targetSettings.aimMode &&
            Math.abs(heroSettings.sensitivity - targetSettings.sensitivity) < 2.0f &&
            Math.abs(heroSettings.aimDurationMillis - targetSettings.aimDurationMillis) < 1.0f
        }
    }
}
