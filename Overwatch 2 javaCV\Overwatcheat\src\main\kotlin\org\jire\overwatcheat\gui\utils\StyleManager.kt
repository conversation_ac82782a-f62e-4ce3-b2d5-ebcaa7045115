/*
 * Style Manager
 * Handles CSS styling and theming for the Arabic GUI
 */

package org.jire.overwatcheat.gui.utils

import javafx.scene.Scene
import javafx.scene.control.*
import javafx.scene.layout.Pane

object StyleManager {
    
    // ألوان التطبيق
    object Colors {
        const val PRIMARY = "#FF6B35"        // برتقالي Overwatch
        const val SECONDARY = "#F7931E"      // برتقالي فاتح
        const val ACCENT = "#FFE66D"         // أصفر
        const val BACKGROUND = "#1E1E1E"     // رمادي داكن
        const val SURFACE = "#2D2D2D"        // رمادي متوسط
        const val ON_SURFACE = "#FFFFFF"     // أبيض
        const val ON_PRIMARY = "#FFFFFF"     // أبيض
        const val SUCCESS = "#4CAF50"        // أخضر
        const val WARNING = "#FF9800"        // برتقالي تحذير
        const val ERROR = "#F44336"          // أحمر
        const val INFO = "#2196F3"           // أزرق
    }
    
    /**
     * تطبيق الستايل العربي على المشهد
     */
    fun applyArabicStyle(scene: Scene) {
        // تطبيق CSS مخصص
        scene.stylesheets.add(createMainStylesheet())
        
        // تطبيق الخطوط العربية على جميع العناصر
        applyArabicFonts(scene.root)
    }
    
    /**
     * إنشاء ملف CSS الرئيسي
     */
    private fun createMainStylesheet(): String {
        // إنشاء CSS كـ Data URL
        val css = """
            /* الستايل الرئيسي للواجهة العربية */
            
            .root {
                -fx-font-family: "Tahoma", "Arial Unicode MS", "Segoe UI";
                -fx-font-size: 14px;
                -fx-background-color: ${Colors.BACKGROUND};
                -fx-text-fill: ${Colors.ON_SURFACE};
            }
            
            /* شريط القائمة */
            .menu-bar {
                -fx-background-color: ${Colors.SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 0 0 2 0;
            }
            
            .menu-bar .menu {
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-font-size: 13px;
            }
            
            .menu-bar .menu:hover {
                -fx-background-color: ${Colors.PRIMARY};
            }
            
            .menu-item {
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-background-color: ${Colors.SURFACE};
            }
            
            .menu-item:hover {
                -fx-background-color: ${Colors.PRIMARY};
            }
            
            /* التبويبات */
            .tab-pane {
                -fx-background-color: ${Colors.BACKGROUND};
            }
            
            .tab-pane .tab-header-area {
                -fx-background-color: ${Colors.SURFACE};
            }
            
            .tab {
                -fx-background-color: ${Colors.SURFACE};
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-font-size: 13px;
                -fx-padding: 8 16 8 16;
            }
            
            .tab:selected {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-text-fill: ${Colors.ON_PRIMARY};
            }
            
            .tab:hover {
                -fx-background-color: ${Colors.SECONDARY};
            }
            
            /* الأزرار */
            .button {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-text-fill: ${Colors.ON_PRIMARY};
                -fx-font-size: 12px;
                -fx-padding: 8 16 8 16;
                -fx-background-radius: 4;
                -fx-border-radius: 4;
                -fx-cursor: hand;
            }
            
            .button:hover {
                -fx-background-color: ${Colors.SECONDARY};
                -fx-scale-x: 1.05;
                -fx-scale-y: 1.05;
            }
            
            .button:pressed {
                -fx-background-color: derive(${Colors.PRIMARY}, -20%);
                -fx-scale-x: 0.95;
                -fx-scale-y: 0.95;
            }
            
            .button:disabled {
                -fx-background-color: #666666;
                -fx-text-fill: #999999;
                -fx-opacity: 0.6;
            }
            
            /* أزرار خاصة */
            .success-button {
                -fx-background-color: ${Colors.SUCCESS};
            }
            
            .warning-button {
                -fx-background-color: ${Colors.WARNING};
            }
            
            .error-button {
                -fx-background-color: ${Colors.ERROR};
            }
            
            .info-button {
                -fx-background-color: ${Colors.INFO};
            }
            
            /* التسميات */
            .label {
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-font-size: 13px;
            }
            
            .title-label {
                -fx-font-size: 18px;
                -fx-font-weight: bold;
                -fx-text-fill: ${Colors.PRIMARY};
            }
            
            .subtitle-label {
                -fx-font-size: 16px;
                -fx-font-weight: bold;
                -fx-text-fill: ${Colors.ON_SURFACE};
            }
            
            .section-label {
                -fx-font-size: 14px;
                -fx-font-weight: bold;
                -fx-text-fill: ${Colors.SECONDARY};
            }
            
            /* حقول الإدخال */
            .text-field, .text-area {
                -fx-background-color: ${Colors.SURFACE};
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 1;
                -fx-border-radius: 4;
                -fx-background-radius: 4;
                -fx-padding: 6;
            }
            
            .text-field:focused, .text-area:focused {
                -fx-border-color: ${Colors.SECONDARY};
                -fx-border-width: 2;
            }
            
            /* المنزلقات */
            .slider {
                -fx-background-color: ${Colors.SURFACE};
            }
            
            .slider .track {
                -fx-background-color: #666666;
                -fx-background-radius: 2;
            }
            
            .slider .thumb {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-background-radius: 8;
            }
            
            .slider .thumb:hover {
                -fx-background-color: ${Colors.SECONDARY};
            }
            
            /* مربعات الاختيار */
            .check-box {
                -fx-text-fill: ${Colors.ON_SURFACE};
            }
            
            .check-box .box {
                -fx-background-color: ${Colors.SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 1;
                -fx-border-radius: 2;
                -fx-background-radius: 2;
            }
            
            .check-box:selected .mark {
                -fx-background-color: ${Colors.PRIMARY};
            }
            
            /* القوائم المنسدلة */
            .choice-box, .combo-box {
                -fx-background-color: ${Colors.SURFACE};
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 1;
                -fx-border-radius: 4;
                -fx-background-radius: 4;
            }
            
            .choice-box:hover, .combo-box:hover {
                -fx-border-color: ${Colors.SECONDARY};
            }
            
            /* الجداول */
            .table-view {
                -fx-background-color: ${Colors.BACKGROUND};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 1;
            }
            
            .table-view .column-header {
                -fx-background-color: ${Colors.SURFACE};
                -fx-text-fill: ${Colors.ON_SURFACE};
                -fx-font-weight: bold;
            }
            
            .table-row-cell {
                -fx-background-color: ${Colors.SURFACE};
                -fx-text-fill: ${Colors.ON_SURFACE};
            }
            
            .table-row-cell:selected {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-text-fill: ${Colors.ON_PRIMARY};
            }
            
            /* شريط التمرير */
            .scroll-bar {
                -fx-background-color: ${Colors.SURFACE};
            }
            
            .scroll-bar .thumb {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-background-radius: 4;
            }
            
            .scroll-bar .thumb:hover {
                -fx-background-color: ${Colors.SECONDARY};
            }
            
            /* شريط الحالة */
            .status-bar {
                -fx-background-color: ${Colors.SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 2 0 0 0;
                -fx-padding: 5 10 5 10;
            }
            
            /* الحاويات */
            .titled-pane {
                -fx-background-color: ${Colors.SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 1;
                -fx-border-radius: 4;
                -fx-background-radius: 4;
            }
            
            .titled-pane .title {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-text-fill: ${Colors.ON_PRIMARY};
                -fx-font-weight: bold;
            }
            
            /* الفواصل */
            .separator {
                -fx-background-color: ${Colors.PRIMARY};
            }
            
            /* رسائل التنبيه */
            .alert {
                -fx-background-color: ${Colors.BACKGROUND};
            }
            
            .alert .header-panel {
                -fx-background-color: ${Colors.PRIMARY};
            }
            
            .alert .content {
                -fx-background-color: ${Colors.SURFACE};
                -fx-text-fill: ${Colors.ON_SURFACE};
            }
            
            /* تأثيرات خاصة */
            .glow-effect {
                -fx-effect: dropshadow(gaussian, ${Colors.PRIMARY}, 10, 0.5, 0, 0);
            }
            
            .hero-card {
                -fx-background-color: ${Colors.SURFACE};
                -fx-border-color: ${Colors.PRIMARY};
                -fx-border-width: 2;
                -fx-border-radius: 8;
                -fx-background-radius: 8;
                -fx-padding: 10;
                -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0.3, 2, 2);
            }
            
            .hero-card:hover {
                -fx-border-color: ${Colors.SECONDARY};
                -fx-scale-x: 1.02;
                -fx-scale-y: 1.02;
            }
            
            .hero-card.selected {
                -fx-background-color: ${Colors.PRIMARY};
                -fx-border-color: ${Colors.ACCENT};
            }
        """.trimIndent()
        
        return "data:text/css;base64," + java.util.Base64.getEncoder().encodeToString(css.toByteArray())
    }
    
    /**
     * تطبيق الخطوط العربية على جميع العناصر
     */
    private fun applyArabicFonts(node: javafx.scene.Node) {
        when (node) {
            is Label -> {
                node.font = when {
                    node.styleClass.contains("title-label") -> ArabicFontLoader.getTitleFont(18.0)
                    node.styleClass.contains("subtitle-label") -> ArabicFontLoader.getTitleFont(16.0)
                    node.styleClass.contains("section-label") -> ArabicFontLoader.getArabicBoldFont(14.0)
                    else -> ArabicFontLoader.getLabelFont()
                }
            }
            is Button -> {
                node.font = ArabicFontLoader.getButtonFont()
            }
            is MenuItem -> {
                // تطبيق خط القائمة
            }
            is Tab -> {
                // تطبيق خط التبويب
            }
            is Pane -> {
                // تطبيق على جميع العناصر الفرعية
                node.children.forEach { applyArabicFonts(it) }
            }
        }
    }
    
    /**
     * تطبيق ستايل خاص على عنصر
     */
    fun applyCustomStyle(node: javafx.scene.Node, styleClass: String) {
        node.styleClass.add(styleClass)
    }
    
    /**
     * إنشاء ستايل مخصص لبطل معين
     */
    fun createHeroStyle(heroName: String): String {
        // يمكن إضافة ألوان مخصصة لكل بطل
        return when (heroName.lowercase()) {
            "tracer" -> "hero-tracer"
            "widowmaker" -> "hero-widowmaker"
            "reinhardt" -> "hero-reinhardt"
            else -> "hero-default"
        }
    }
    
    /**
     * تطبيق ثيم مظلم
     */
    fun applyDarkTheme(scene: Scene) {
        applyArabicStyle(scene)
    }
    
    /**
     * تطبيق ثيم فاتح
     */
    fun applyLightTheme(scene: Scene) {
        // يمكن إضافة ثيم فاتح لاحقاً
        applyArabicStyle(scene)
    }
}
