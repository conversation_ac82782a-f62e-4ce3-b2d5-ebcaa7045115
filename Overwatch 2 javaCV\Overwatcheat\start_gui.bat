@echo off
title Overwatch 2 Aim Assistant - Arabic GUI
echo ========================================
echo    مساعد التصويب - Overwatch 2
echo    الواجهة العربية الاحترافية
echo ========================================
echo.

REM التحقق من وجود Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت على النظام
    echo يرجى تثبيت Java 17 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود ملف JAR
if not exist "build\libs\Overwatcheat-*.jar" (
    echo خطأ: ملف JAR غير موجود
    echo يرجى بناء المشروع أولاً باستخدام: gradlew build
    pause
    exit /b 1
)

echo بدء تشغيل الواجهة العربية...
echo.

REM تشغيل الواجهة مع معاملات محسنة
echo جاري تشغيل الواجهة العربية...
java -Xmx1G -Dfile.encoding=UTF-8 -Djava.awt.headless=false -cp "build/classes/kotlin/main;build/resources/main" org.jire.overwatcheat.gui.SimpleGUI

if %errorlevel% neq 0 (
    echo.
    echo محاولة تشغيل من ملف JAR...
    for %%f in (build\libs\Overwatcheat-*.jar) do (
        java -Xmx1G -Dfile.encoding=UTF-8 -Djava.awt.headless=false -jar "%%f" --gui
    )
)

if %errorlevel% neq 0 (
    echo.
    echo خطأ في تشغيل التطبيق
    echo كود الخطأ: %errorlevel%
    pause
)
